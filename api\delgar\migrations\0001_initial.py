# Generated by Django 5.0.2 on 2025-07-15 01:39

import django_jsonform.models.fields
import storages.backends.s3
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='FileUploadModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(storage=storages.backends.s3.S3Storage(), upload_to='registrar/appointment/uploads/')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='OrderDetailsModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_id', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('fullname', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('address', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('contact_email', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('contact_number', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('system_fee', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('delivery_fee', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('grand_total', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('payment_method', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('receipt_url', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('products', django_jsonform.models.fields.JSONField(null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ('-created_at',),
            },
        ),
        migrations.CreateModel(
            name='ProductListModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('product_id', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('product_image', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('product_name', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('wholesale_price', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('retail_price', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('category', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('status', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ('-created_at',),
            },
        ),
    ]
