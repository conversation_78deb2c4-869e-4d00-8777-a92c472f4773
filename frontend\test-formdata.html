<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FormData Test</title>
</head>
<body>
    <h1>FormData Test</h1>
    <input type="file" id="fileInput" multiple>
    <button onclick="testFormData()">Test FormData</button>
    <div id="result"></div>

    <script>
        function testFormData() {
            const fileInput = document.getElementById('fileInput');
            const files = fileInput.files;
            const result = document.getElementById('result');
            
            if (files.length === 0) {
                result.innerHTML = 'Please select files first';
                return;
            }
            
            try {
                // Test FormData creation
                const formData = new FormData();
                
                for (let i = 0; i < files.length; i++) {
                    formData.append('file', files[i]);
                    console.log(`Added file: ${files[i].name}`);
                }
                
                result.innerHTML = `✅ FormData created successfully with ${files.length} files`;
                console.log('FormData test passed');
                
            } catch (error) {
                result.innerHTML = `❌ FormData error: ${error.message}`;
                console.error('FormData test failed:', error);
            }
        }
    </script>
</body>
</html>
