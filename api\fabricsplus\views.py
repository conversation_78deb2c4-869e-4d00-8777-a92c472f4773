from rest_framework.response import Response
from rest_framework.views import APIView
from .forms import ContactListForm
from .models import ContactListModel
from .serializers import ContactListSerializer
from rest_framework import generics, parsers, status
from django.template.loader import render_to_string
from django.core import mail
from django.utils.html import strip_tags
from django.core.mail import send_mail
import json  # Add this import
from django.core.mail import EmailMultiAlternatives
import traceback

# Create your views here.

class ContactListView(APIView):
  def get(self, request, format=None):
    obj = ContactListModel.objects.all()
    serializer = ContactListSerializer(obj, many=True)
    
    return Response(serializer.data)

class ContactCreateView(APIView):
  def post(self, request):
    form = ContactListForm(request.data)

    if form.is_valid():
      obj = form.save(commit=False)
      obj.save()

      return Response({'status':'created'})
    else:
      return Response({'status':'errors', 'errors': form.errors})
  
  def put(self, request, pk):
    obj = ContactListModel.objects.get(pk=pk)
    form = ContactListForm(request.data, instance=obj)
    form.save()

    return Response({'status': 'updated'})

  def delete(self, request, pk):
    obj = ContactListModel.objects.get(pk=pk)
    obj.delete()

    return Response({'status': 'deleted'})
  
class ContactDetailView(APIView):
  def get(self, request, pk, format=None):
    obj = ContactListModel.objects.get(pk=pk)
    serializer = ContactListSerializer(obj)

    return Response(serializer.data)
  
class SubmitToGmail(APIView):
  def post(self, request):
    form = ContactListForm(request.data)
    context = {
      "contact_id" : form['contact_id'].value(),
      "firstname" : form['firstname'].value(),
      "lastname" : form['lastname'].value(),
      "contact_email" : form['contact_email'].value(),
      "contact_number" : form['contact_number'].value(),
      "message" : form['message'].value(),
    }
    if form.is_valid():
      subject = 'Inquiry from ' + form.cleaned_data.get('firstname') + ' ' + form.cleaned_data.get('lastname')
      html_message = render_to_string('contact.html', context)
      plain_message = strip_tags(html_message)
      recipient = form.cleaned_data.get('contact_email')
      recipient_list = [recipient]
      email_from = 'Inquiry'
      mail.send_mail(subject, plain_message, email_from, recipient_list, html_message=html_message, fail_silently=False)
      return Response({'status':'sent'})