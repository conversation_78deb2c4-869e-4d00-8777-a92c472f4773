!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("react-modal"),require("react-dom")):"function"==typeof define&&define.amd?define(["exports","react","react-modal","react-dom"],t):t((e||self).reactJsonForm={},e.<PERSON>act,e.ReactModal,e.ReactDOM)}(this,function(e,t,a,r){function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var i=/*#__PURE__*/n(t),l=/*#__PURE__*/n(a),o=/*#__PURE__*/n(r);function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e},s.apply(this,arguments)}function u(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,c(e,t)}function c(e,t){return c=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},c(e,t)}function d(e,t){if(null==e)return{};var a,r,n={},i=Object.keys(e);for(r=0;r<i.length;r++)t.indexOf(a=i[r])>=0||(n[a]=e[a]);return n}function p(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,r=new Array(t);a<t;a++)r[a]=e[a];return r}var f="§",m="__RJF_FILLER__",h=/*#__PURE__*/i.default.createContext();function v(e){return e?e.charAt(0).toUpperCase()+e.substr(1).toLowerCase():""}function g(e,t){return typeof e===t||("number"===t||"integer"===t?"string"==typeof e?""===(e=e.trim())?e=null:isNaN(Number(e))||(e=Number(e)):"boolean"==typeof e&&(e=!0===e?1:0):"boolean"===t&&(e="false"!==e&&!1!==e)),e}function y(e){var t=typeof e;return"object"===t&&(Array.isArray(e)?t="array":null===e&&(t="null")),t}function O(e){var t;return(t=e.hasOwnProperty("const")?y(e.const):k(e.type))||(t=e.hasOwnProperty("properties")||e.hasOwnProperty("keys")?"object":e.hasOwnProperty("items")?"array":e.hasOwnProperty("allOf")?"allOf":e.hasOwnProperty("oneOf")?"oneOf":e.hasOwnProperty("anyOf")?"anyOf":"string"),t}function E(){var e=document.cookie.split(";").filter(function(e){return 0===e.trim().indexOf("csrftoken=")});if(e.length)return e[0].split("=")[1];var t=document.querySelector('input[name="csrfmiddlewaretoken"]');return t?t.value:null}function b(){return Array.from(arguments).join(f)}function w(e){return e.split(f)}function j(e){return e.slice("rjf§".length)}function k(e){switch(Array.isArray(e)&&(e=e.find(function(e){return"null"!==e})||"null"),e){case"list":return"array";case"dict":return"object";case"keys":return"properties";case"choices":return"enum";case"datetime":return"date-time";default:return e}}function S(e,t,a,r){return N(e,t,N(e,a,r))}function N(e,t,a){var r=e[t];return void 0!==r?r:a}function C(e,t){var a=S(e,"choices","enum");return!a||void 0!==a.find(function(e){return"object"==typeof e&&(e=e.value),t==e})}function x(e,t){var a={},r=S(e,"keys","properties",{});for(var n in r){var i=r[n],l=i.hasOwnProperty("$ref"),o=i.hasOwnProperty("const");l&&delete(i=s({},t(i.$ref),i)).$ref;var u=k(i.type);u||(i.hasOwnProperty("oneOf")?i=i.oneOf[0]:i.hasOwnProperty("anyOf")&&(i=i.anyOf[0]),u=k(i.type));var c=i.default;o&&(u=y(i.const),c=i.const),a[n]="array"===u?l?[]:P(i,t):"object"===u?x(i,t):"boolean"===u?!1!==c&&(c||null):"integer"===u||"number"===u?0===c?0:c||null:c||""}if(e.hasOwnProperty("oneOf")&&(a=s({},a,x(e.oneOf[0]))),e.hasOwnProperty("anyOf")&&(a=s({},a,x(e.anyOf[0]))),e.hasOwnProperty("allOf"))for(var d=0;d<e.allOf.length;d++)a=s({},a,x(e.allOf[d]));return a}function P(e,t){var a=S(e,"minItems","min_items")||0;if(e.default&&e.default.length>=a)return e.default;var r=[];if(e.default&&(r=[].concat(e.default)),0===a)return r;e.items.hasOwnProperty("$ref")&&(e.items=s({},t(e.items.$ref),e.items),delete e.items.$ref);var n=k(e.items.type);if(n||(Array.isArray(e.items.oneOf)?n=O(e.items.oneOf[0]):Array.isArray(e.items.anyOf)?n=O(e.items.anyOf[0]):Array.isArray(e.items.allOf)?n=O(e.items.allOf[0]):e.items.hasOwnProperty("const")&&(n=y(e.items.const))),"array"===n){for(;r.length<a;)r.push(P(e.items,t));return r}if("object"===n){for(;r.length<a;)r.push(x(e.items,t));return r}if("oneOf"===n){for(;r.length<a;)r.push(A(e.items,t));return r}if("anyOf"===n){for(;r.length<a;)r.push(A(e.items,t));return r}if("multiselect"===e.items.widget)return r;var i=e.items.default;if(e.items.hasOwnProperty("const")&&(i=e.items.const),"boolean"===n)for(;r.length<a;)r.push(!1!==i&&(i||null));else if("integer"===n||"number"===n)for(;r.length<a;)r.push(0===i?0:i||null);else for(;r.length<a;)r.push(i||"");return r}function A(e,t){var a=e.oneOf[0];return O(a),R(a,t)}function R(e,t){e.hasOwnProperty("$ref")&&delete(e=s({},t(e.$ref),e)).$ref;var a=O(e),r=e.default;return e.hasOwnProperty("const")&&(a=y(e.const),r=e.const),"array"===a?P(e,t):"object"===a?x(e,t):"allOf"===a?function(e,t){return x(e,t)}(e,t):"oneOf"===a?A(e,t):"anyOf"===a?function(e,t){var a=e.anyOf[0];return O(a),R(a,t)}(e,t):"boolean"===a?!1!==r&&(r||null):"integer"===a||"number"===a?0===r?0:r||null:r||""}function _(e,t,a){if(null===e&&(e=[]),"array"!==y(e))throw new Error("Schema expected an 'array' but the data type was '"+y(e)+"'");var r,n,i=JSON.parse(JSON.stringify(e));t.items.hasOwnProperty("$ref")&&(t.items=s({},a(t.items.$ref),t.items),delete t.items.$ref),t.items.hasOwnProperty("const")?(r=y(t.items.const),n=t.items.const):(r=k(t.items.type),n=t.items.defualt);for(var l=t.minItems||t.min_items||0;e.length<l;)e.push(m);for(var o=0;o<e.length;o++){var u=e[o];"array"===r?(u===m&&(u=[]),i[o]=_(u,t.items,a)):"object"===r?(u===m&&(u={}),i[o]=M(u,t.items,a)):(C(t.items,i[o])||(u=m),u===m&&(i[o]="integer"===r||"number"===r?0===n?0:n||null:"boolean"===r?!1!==n&&(n||null):n||"")),t.items.hasOwnProperty("const")&&(i[o]=t.items.const)}return i}function M(e,t,a){if(null===e&&(e={}),"object"!==y(e))throw new Error("Schema expected an 'object' but the data type was '"+y(e)+"'");var r=JSON.parse(JSON.stringify(e)),n=S(t,"keys","properties",{});if(t.hasOwnProperty("allOf"))for(var i=0;i<t.allOf.length;i++)"object"===O(t.allOf[i])&&(n=s({},n,S(t.allOf[i],"properties","keys",{})));for(var l=[].concat(Object.keys(n)),o=0;o<l.length;o++){var u=l[o],c=n[u];c.hasOwnProperty("$ref")&&delete(c=s({},a(c.$ref),c)).$ref;var d=void 0,p=void 0;c.hasOwnProperty("const")?(d=y(c.const),p=c.const):(d=O(c),p=c.default),e.hasOwnProperty(u)?"array"===d?r[u]=_(e[u],c,a):"object"===d?r[u]=M(e[u],c,a):"oneOf"===d?r[u]=D(e[u],c,a):"anyOf"===d?r[u]=V(e[u],c,a):(C(c,e[u])||(e[u]=""),r[u]=""===e[u]?"integer"===d||"number"===d?0===p?0:p||null:"boolean"===d?!1!==p&&(p||null):p||"":e[u]):r[u]="array"===d?_([],c,a):"object"===d?M({},c,a):"oneOf"===d?A(c,a):"anyOf"===d?getBlankAntOf(c,a):"boolean"===d?!1!==p&&(p||null):"integer"===d||"number"===d?0===p?0:p||null:p||"",c.hasOwnProperty("const")&&(r[u]=c.const)}return t.hasOwnProperty("oneOf")&&(r=s({},r,D(e,t,a))),t.hasOwnProperty("anyOf")&&(r=s({},r,V(e,t,a))),r}function I(e,t,a){return M(e,t,a)}function D(e,t,a){var r=L(e,t,a,"oneOf"),n=t.oneOf[r],i=T(O(n));return i?i(e,n,a):e}function V(e,t,a){var r=L(e,t,a,"anyOf"),n=t.anyOf[r],i=T(O(n));return i?i(e,n,a):e}function T(e){return"array"===e?_:"object"===e?M:"allOf"===e?I:"oneOf"===e?D:"anyOf"===e?V:null}function L(e,t,a,r){for(var n=y(e),i=t[r],l=null,o=0;o<i.length;o++){var u=i[o];u.hasOwnProperty("$ref")&&delete(u=s({},a(u.$ref),u)).$ref;var c=O(u);if("object"===n){if($(e,u)){l=o;break}}else if("array"===n){if(F(e,u)){l=o;break}}else if(n===c){l=o;break}}if(null===l)for(var d=0;d<i.length;d++){var p=i[d];if(p.hasOwnProperty("$ref")&&delete(p=s({},a(p.$ref),p)).$ref,n===O(p)){l=d;break}}if(null===l){if(null!==e)throw new Error("No matching subschema found in '"+r+"' for data '"+e+"' (type: "+n+")");l=0}return l}function $(e,t){var a=y(e);if(O(t)!==a)return!1;var r,n,i=S(t,"properties","keys",{});if(keyset1=new Set(Object.keys(e)),keyset2=new Set(Object.keys(i)),t.hasOwnProperty("additionalProperties")){if(!function(e,t){for(var a,r=function(e,t){var a="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(a)return(a=a.call(e)).next.bind(a);if(Array.isArray(e)||(a=function(e,t){if(e){if("string"==typeof e)return p(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?p(e,t):void 0}}(e))){a&&(e=a);var r=0;return function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(e);!(a=r()).done;)if(!t.has(a.value))return!1;return!0}(keyset2,keyset1))return!1}else if(r=keyset2,n=keyset1,r.size!==n.size||!Array.from(r).every(function(e){return n.has(e)}))return!1;for(var l in i)if(i.hasOwnProperty(l)){if(!e.hasOwnProperty(l))return!1;if(i[l].hasOwnProperty("const")&&i[l].const!==e[l])return!1;var o=k(i[l].type),s=y(e[l]);if("number"===o&&-1===["number","integer","null"].indexOf(s))return!1;if("integer"===o&&-1===["number","integer","null"].indexOf(s))return!1;if("boolean"===o&&-1===["boolean","null"].indexOf(s))return!1;if("string"===o&&"string"!==s)return!1}return!0}function F(e,t){var a=y(e);if(O(t)!==a)return!1;for(var r=t.items.type,n=0;n<e.length;n++){if(dataValueType=y(e[n]),t.items.hasOwnProperty("const")&&t.items.const!==e[n])return!1;if("number"===r&&-1===["number","integer","null"].indexOf(dataValueType))return!1;if("integer"===r&&-1===["number","integer","null"].indexOf(dataValueType))return!1;if("boolean"===r&&-1===["boolean","null"].indexOf(dataValueType))return!1;if("string"===r&&"string"!==dataValueType)return!1}return!0}var q=["className","alterClassName"];function H(e){var t=e.className,a=e.alterClassName,r=d(e,q);t||(t="");var n=t.split(" ");if(!1!==a){t="";for(var i=0;i<n.length;i++)t=t+"rjf-"+n[i]+"-button "}/*#__PURE__*/return React.createElement("button",s({className:t.trim(),type:"button"},r),r.children)}function U(e){var t="rjf-loader";return e.className&&(t=t+" "+e.className),/*#__PURE__*/React.createElement("div",{className:t})}function J(e){var t;switch(e.name){case"chevron-up":t=/*#__PURE__*/i.default.createElement(B,null);break;case"chevron-down":t=/*#__PURE__*/i.default.createElement(z,null);break;case"arrow-down":t=/*#__PURE__*/i.default.createElement(K,null);break;case"x-lg":t=/*#__PURE__*/i.default.createElement(W,null);break;case"x-circle":t=/*#__PURE__*/i.default.createElement(Y,null);break;case"three-dots-vertical":t=/*#__PURE__*/i.default.createElement(G,null)}/*#__PURE__*/return i.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"currentColor",className:"rjf-icon rjf-icon-"+e.name,viewBox:"0 0 16 16"},t)}function B(e){/*#__PURE__*/return i.default.createElement("path",{fillRule:"evenodd",d:"M7.646 4.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1-.708.708L8 5.707l-5.646 5.647a.5.5 0 0 1-.708-.708l6-6z"})}function z(e){/*#__PURE__*/return i.default.createElement("path",{fillRule:"evenodd",d:"M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z"})}function K(e){/*#__PURE__*/return i.default.createElement("path",{"fill-rule":"evenodd",d:"M8 1a.5.5 0 0 1 .5.5v11.793l3.146-3.147a.5.5 0 0 1 .708.708l-4 4a.5.5 0 0 1-.708 0l-4-4a.5.5 0 0 1 .708-.708L7.5 13.293V1.5A.5.5 0 0 1 8 1z"})}function W(e){/*#__PURE__*/return i.default.createElement("path",{d:"M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8 2.146 2.854Z"})}function Y(e){/*#__PURE__*/return i.default.createElement(i.default.Fragment,null,/*#__PURE__*/i.default.createElement("path",{d:"M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"}),/*#__PURE__*/i.default.createElement("path",{d:"M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z"}))}function G(e){/*#__PURE__*/return i.default.createElement("path",{d:"M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"})}var X=/*#__PURE__*/function(e){function t(){for(var t,a=arguments.length,r=new Array(a),n=0;n<a;n++)r[n]=arguments[n];return(t=e.call.apply(e,[this].concat(r))||this).sendValue=function(e){t.props.onChange(e)},t.validateValue=function(e,t){return"hh"===e&&t<1?12:"hh"!==e&&t<0?59:"hh"===e&&t>12?1:"hh"!==e&&t>59?0:t},t.handleChange=function(e){var a,r=e.target.dataset.name,n=e.target.value;if(!isNaN(n)){var i=t.validateValue(r,parseInt(n)||0);"hh"!==r||"0"!==n&&""!==n&&"00"!==n||1!==i||(i=0),n.startsWith("0")&&i<10&&0!==i&&(i=i.toString().padStart(2,"0")),t.sendValue(((a={})[r]=""!==n?i.toString():"",a))}},t.handleKeyDown=function(e){var a;if(38===e.keyCode||40===e.keyCode){var r=e.target.dataset.name,n=parseInt(e.target.value)||0;38===e.keyCode?n++:40===e.keyCode&&n--,t.sendValue(((a={})[r]=t.validateValue(r,n).toString().padStart(2,"0"),a))}},t.handleSpin=function(e,a){var r,n=t.props[e];"ampm"===e?n="am"===n?"pm":"am":(n=parseInt(n)||0,"up"===a?n++:n--,n=t.validateValue(e,n).toString().padStart(2,"0")),t.sendValue(((r={})[e]=n,r))},t.handleBlur=function(e){var a,r=t.validateValue(e.target.dataset.name,parseInt(e.target.value)||0);r<10&&t.sendValue(((a={})[e.target.dataset.name]=r.toString().padStart(2,"0"),a))},t}u(t,e);var a=t.prototype;return a.componentWillUnmount=function(){var e={hh:this.validateValue("hh",this.props.hh).toString().padStart(2,"0"),mm:this.validateValue("mm",this.props.mm).toString().padStart(2,"0"),ss:this.validateValue("ss",this.props.ss).toString().padStart(2,"0")};this.sendValue(e)},a.render=function(){var e=this;/*#__PURE__*/return i.default.createElement("div",{className:"rjf-time-picker"},/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-row rjf-time-picker-labels"},/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-col"},"Hrs"),/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-col rjf-time-picker-col-sm"}),/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-col"},"Min"),/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-col rjf-time-picker-col-sm"}),/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-col"},"Sec"),/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-col rjf-time-picker-col-sm"}),/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-col"},"am/pm")),/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-row"},/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-col"},/*#__PURE__*/i.default.createElement(H,{onClick:function(){return e.handleSpin("hh","up")}},/*#__PURE__*/i.default.createElement(J,{name:"chevron-up"}))),/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-col rjf-time-picker-col-sm"}),/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-col"},/*#__PURE__*/i.default.createElement(H,{onClick:function(){return e.handleSpin("mm","up")}},/*#__PURE__*/i.default.createElement(J,{name:"chevron-up"}))),/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-col rjf-time-picker-col-sm"}),/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-col"},/*#__PURE__*/i.default.createElement(H,{onClick:function(){return e.handleSpin("ss","up")}},/*#__PURE__*/i.default.createElement(J,{name:"chevron-up"}))),/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-col rjf-time-picker-col-sm"}),/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-col"},/*#__PURE__*/i.default.createElement(H,{onClick:function(){return e.handleSpin("ampm","up")}},/*#__PURE__*/i.default.createElement(J,{name:"chevron-up"})))),/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-row rjf-time-picker-values"},/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-col"},/*#__PURE__*/i.default.createElement("input",{type:"text","data-name":"hh",value:this.props.hh,onChange:this.handleChange,onBlur:this.handleBlur,onKeyDown:this.handleKeyDown})),/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-col rjf-time-picker-col-sm"},":"),/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-col"},/*#__PURE__*/i.default.createElement("input",{type:"text","data-name":"mm",value:this.props.mm,onChange:this.handleChange,onBlur:this.handleBlur,onKeyDown:this.handleKeyDown})),/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-col rjf-time-picker-col-sm"},":"),/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-col"},/*#__PURE__*/i.default.createElement("input",{type:"text","data-name":"ss",value:this.props.ss,onChange:this.handleChange,onBlur:this.handleBlur,onKeyDown:this.handleKeyDown})),/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-col rjf-time-picker-col-sm"}),/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-col"},this.props.ampm)),/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-row"},/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-col"},/*#__PURE__*/i.default.createElement(H,{onClick:function(){return e.handleSpin("hh","down")}},/*#__PURE__*/i.default.createElement(J,{name:"chevron-down"}))),/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-col rjf-time-picker-col-sm"}),/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-col"},/*#__PURE__*/i.default.createElement(H,{onClick:function(){return e.handleSpin("mm","down")}},/*#__PURE__*/i.default.createElement(J,{name:"chevron-down"}))),/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-col rjf-time-picker-col-sm"}),/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-col"},/*#__PURE__*/i.default.createElement(H,{onClick:function(){return e.handleSpin("ss","down")}},/*#__PURE__*/i.default.createElement(J,{name:"chevron-down"}))),/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-col rjf-time-picker-col-sm"}),/*#__PURE__*/i.default.createElement("div",{className:"rjf-time-picker-col"},/*#__PURE__*/i.default.createElement(H,{onClick:function(){return e.handleSpin("ampm","down")}},/*#__PURE__*/i.default.createElement(J,{name:"chevron-down"})))))},t}(i.default.Component),Z=["label","help_text","error","inputRef"],Q=["label","help_text","error","value"],ee=["label","help_text","error","value","options"],te=["label","help_text","error","value","options"],ae=["label","value"],re=["label","help_text","error","inputRef"];function ne(e){return e.label?/*#__PURE__*/i.default.createElement("label",{className:e.required?"rjf-required":null},e.children,e.children&&" ",e.label):null}function ie(e){var t=e.label,a=e.help_text,r=e.error,n=e.inputRef,l=d(e,Z);"string"===l.type&&(l.type="text"),n&&(l.ref=n),null===l.value&&(l.value="");var o={};return"hidden"==l.type&&(o.style={display:"none"}),l.readOnly&&null==l.disabled&&(l.disabled=!0),/*#__PURE__*/i.default.createElement("div",o,/*#__PURE__*/i.default.createElement(ne,{label:t,required:l.required}),/*#__PURE__*/i.default.createElement("div",{className:r?"rjf-input-group has-error":"rjf-input-group"},l.children||/*#__PURE__*/i.default.createElement("input",l),r&&r.map(function(e,t){/*#__PURE__*/return i.default.createElement("span",{className:"rjf-error-text",key:t},e)}),a&&/*#__PURE__*/i.default.createElement("span",{className:"rjf-help-text"},a)))}function le(e){var t=e.label,a=e.help_text,r=e.error,n=e.value,l=d(e,Q);return t||(t=l.name.toUpperCase()),"bool"===l.type&&(l.type="checkbox"),void 0===l.checked&&(l.checked=n),""!==l.checked&&null!=l.checked||(l.checked=!1),l.readOnly&&(l.disabled=!0),/*#__PURE__*/i.default.createElement("div",{className:r?"rjf-check-input has-error":"rjf-check-input"},/*#__PURE__*/i.default.createElement(ne,{label:t,required:l.required},/*#__PURE__*/i.default.createElement("input",l)),r&&r.map(function(e,t){/*#__PURE__*/return i.default.createElement("span",{className:"rjf-error-text",key:t},e)}),a&&/*#__PURE__*/i.default.createElement("span",{className:"rjf-help-text"},a))}function oe(e){var t=e.label,a=e.help_text,r=e.error,n=e.value,l=e.options,o=d(e,ee);return o.readOnly&&(o.disabled=!0),/*#__PURE__*/i.default.createElement("div",{className:r?"rjf-check-input has-error":"rjf-check-input"},/*#__PURE__*/i.default.createElement(ne,{label:t,required:o.required}),l.map(function(e,t){var a,r;return"object"==typeof e?(a=e.title||e.label,r=e.value):("boolean"==typeof(a=e)&&(a=v(a.toString())),r=e),/*#__PURE__*/i.default.createElement("label",{className:"rjf-radio-option",key:a+"_"+r+"_"+t},/*#__PURE__*/i.default.createElement("input",s({},o,{value:r,checked:r===n}))," ",a)}),r&&r.map(function(e,t){/*#__PURE__*/return i.default.createElement("span",{className:"rjf-error-text",key:t},e)}),a&&/*#__PURE__*/i.default.createElement("span",{className:"rjf-help-text"},a))}function se(e){var t=e.label,a=e.help_text,r=e.error,n=e.value,l=e.options,o=d(e,te);return o.readOnly&&(o.disabled=!0),n||!1===n||0===n||(n=""),/*#__PURE__*/i.default.createElement("div",null,/*#__PURE__*/i.default.createElement(ne,{label:t,required:o.required}),/*#__PURE__*/i.default.createElement("div",{className:r?"rjf-input-group has-error":"rjf-input-group"},/*#__PURE__*/i.default.createElement("select",s({value:n},o),/*#__PURE__*/i.default.createElement("option",{disabled:!0,value:"",key:"__placeholder"},"Select..."),l.map(function(e,t){var a,r;return"object"==typeof e?(a=e.title||e.label,r=e.value):("boolean"==typeof(a=e)&&(a=v(a.toString())),r=e),/*#__PURE__*/i.default.createElement("option",{value:r,key:a+"_"+r+"_"+t},a)})),r&&r.map(function(e,t){/*#__PURE__*/return i.default.createElement("span",{className:"rjf-error-text",key:t},e)}),a&&/*#__PURE__*/i.default.createElement("span",{className:"rjf-help-text"},a)))}var ue=/*#__PURE__*/function(e){function t(t){var a;return(a=e.call(this,t)||this).handleChange=function(e){var t=[].concat(a.props.value),r=e.target.value;typeof r!==a.props.valueType&&(r=g(r,a.props.valueType)),e.target.checked?t.push(r):t=t.filter(function(e){return e!==r}),a.props.onChange({target:{type:a.props.type,value:t,name:a.props.name}})},a.showOptions=function(e){a.state.showOptions||a.setState({showOptions:!0})},a.hideOptions=function(e){a.setState({showOptions:!1})},a.toggleOptions=function(e){a.setState(function(e){return{showOptions:!e.showOptions}})},a.state={showOptions:!1},a.optionsContainer=/*#__PURE__*/i.default.createRef(),a.input=/*#__PURE__*/i.default.createRef(),a}return u(t,e),t.prototype.render=function(){/*#__PURE__*/return i.default.createElement("div",{className:this.props.readOnly?"rjf-multiselect-field readonly":"rjf-multiselect-field"},/*#__PURE__*/i.default.createElement(ie,{label:this.props.label,help_text:this.props.help_text,error:this.props.error},/*#__PURE__*/i.default.createElement(ce,{inputRef:this.input,onClick:this.toggleOptions,value:this.props.value,options:this.props.options,onChange:this.handleChange,disabled:this.props.readOnly,placeholder:this.props.placeholder})),this.state.showOptions&&/*#__PURE__*/i.default.createElement(de,{options:this.props.options,value:this.props.value,hideOptions:this.hideOptions,onChange:this.handleChange,containerRef:this.optionsContainer,inputRef:this.input,disabled:this.props.readOnly,hasHelpText:(this.props.help_text||this.props.error)&&1}))},t}(i.default.Component),ce=/*#__PURE__*/function(e){function t(){for(var t,a=arguments.length,r=new Array(a),n=0;n<a;n++)r[n]=arguments[n];return(t=e.call.apply(e,[this].concat(r))||this).handleRemove=function(e,a){e.stopPropagation(),t.props.onChange({target:{value:t.props.value[a],checked:!1}})},t}return u(t,e),t.prototype.render=function(){var e=this,t=function(e){for(var t={},a=0;a<e.length;a++){var r=e[a],n=void 0,i=void 0;"object"===y(r)?(n=r.value,i=r.title):(n=r,i=r),t[n]=i}return t}(this.props.options||this.props.value);/*#__PURE__*/return i.default.createElement("div",{className:"rjf-multiselect-field-input",onClick:this.props.onClick,ref:this.props.inputRef,tabIndex:0},this.props.value.length?this.props.value.map(function(a,r){/*#__PURE__*/return i.default.createElement("span",{className:"rjf-multiselect-field-input-item",key:a+"_"+r},/*#__PURE__*/i.default.createElement("span",null,t[a]),e.props.disabled||/*#__PURE__*/i.default.createElement("button",{title:"Remove",type:"button",onClick:function(t){return e.handleRemove(t,r)}},"×"))}):/*#__PURE__*/i.default.createElement("span",{className:"rjf-multiselect-field-input-placeholder"},this.props.placeholder||"Select..."))},t}(i.default.Component),de=/*#__PURE__*/function(e){function t(){for(var t,a=arguments.length,r=new Array(a),n=0;n<a;n++)r[n]=arguments[n];return(t=e.call.apply(e,[this].concat(r))||this).handleClickOutside=function(e){!t.props.containerRef.current||t.props.containerRef.current.contains(e.target)||t.props.inputRef.current.contains(e.target)||t.props.hideOptions()},t}u(t,e);var a=t.prototype;return a.componentDidMount=function(){document.addEventListener("mousedown",this.handleClickOutside)},a.componentWillUnmount=function(){document.removeEventListener("mousedown",this.handleClickOutside)},a.render=function(){var e=this;/*#__PURE__*/return i.default.createElement("div",{ref:this.props.containerRef},/*#__PURE__*/i.default.createElement("div",{className:"rjf-multiselect-field-options-container",style:this.props.hasHelpText?{marginTop:"-15px"}:{}},this.props.options.map(function(t,a){var r,n;"object"==typeof t?(r=t.title||t.label,n=t.value):("boolean"==typeof(r=t)&&(r=v(r.toString())),n=t);var l=e.props.value.indexOf(n)>-1,o="rjf-multiselect-field-option";return l&&(o+=" selected"),e.props.disabled&&(o+=" disabled"),/*#__PURE__*/i.default.createElement("div",{key:r+"_"+n+"_"+a,className:o},/*#__PURE__*/i.default.createElement("label",null,/*#__PURE__*/i.default.createElement("input",{type:"checkbox",onChange:e.props.onChange,value:n,checked:l,disabled:e.props.disabled})," ",r))})))},t}(i.default.Component),pe=/*#__PURE__*/function(e){function t(t){var a;return(a=e.call(this,t)||this).getFileName=function(){return a.props.value?"data-url"===a.props.type?a.extractFileInfo(a.props.value).name:"file-url"===a.props.type?a.props.value:"Unknown file":""},a.extractFileInfo=function(e){var t=function(e){var t,a=e.split(","),r=a[0].split(";"),n=r[0].replace("data:",""),i=r.filter(function(e){return"name"===e.split("=")[0]});t=1!==i.length?"unknown":i[0].split("=")[1];for(var l=atob(a[1]),o=[],s=0;s<l.length;s++)o.push(l.charCodeAt(s));return{blob:new window.Blob([new Uint8Array(o)],{type:n}),name:t}}(e),a=t.blob;return{name:t.name,size:a.size,type:a.type}},a.addNameToDataURL=function(e,t){return e.replace(";base64",";name="+encodeURIComponent(t)+";base64")},a.handleChange=function(e){if("data-url"===a.props.type){var t=e.target.files[0],r=t.name,n=new FileReader;n.onload=function(){var e={target:{type:"text",value:a.addNameToDataURL(n.result,r),name:a.props.name}};a.props.onChange(e)},n.readAsDataURL(t)}else if("file-url"===a.props.type){var i=a.props.handler||a.context.fileHandler;if(!i)return console.error("Error: fileHandler option need to be passed while initializing editor for enabling file uploads."),void alert("Files couldn't be uploaded.");a.setState({loading:!0});var l=new FormData;for(var o in a.context.fileHandlerArgs)a.context.fileHandlerArgs.hasOwnProperty(o)&&l.append(o,a.context.fileHandlerArgs[o]);l.append("coords",j(a.props.name)),l.append("file",e.target.files[0]),fetch(i,{method:"POST",headers:{"X-CSRFToken":E()},body:l}).then(function(e){return e.json()}).then(function(e){a.props.onChange({target:{type:"text",value:e.value,name:a.props.name}}),a.setState({loading:!1})}).catch(function(e){alert("Something went wrong while uploading file"),console.error("Error:",e),a.setState({loading:!1})})}},a.showFileBrowser=function(){a.inputRef.current.click()},a.clearFile=function(){window.confirm("Do you want to remove this file?")&&(a.props.onChange({target:{type:"text",value:"",name:a.props.name}}),a.inputRef.current&&(a.inputRef.current.value=""))},a.state={value:t.value,fileName:a.getFileName(),loading:!1},a.inputRef=/*#__PURE__*/i.default.createRef(),a}u(t,e);var a=t.prototype;return a.componentDidUpdate=function(e,t){this.props.value!==e.value&&this.setState({value:this.props.value,fileName:this.getFileName()})},a.render=function(){var e=s({value:a},this.props),t=e.label,a=e.value,r=d(e,ae);return r.type="file",r.onChange=this.handleChange,r.readOnly&&(r.disabled=!0),/*#__PURE__*/i.default.createElement("div",null,/*#__PURE__*/i.default.createElement(ne,{label:t,required:r.required}),/*#__PURE__*/i.default.createElement("div",{className:"rjf-file-field"},this.state.value&&/*#__PURE__*/i.default.createElement("div",{className:"rjf-current-file-name"},"Current file: ",/*#__PURE__*/i.default.createElement("span",null,this.state.fileName)," "," ",/*#__PURE__*/i.default.createElement(H,{className:"remove-file",onClick:this.clearFile},"Clear")),this.state.value&&!this.state.loading&&"Change:",this.state.loading?/*#__PURE__*/i.default.createElement("div",{className:"rjf-file-field-loading"},/*#__PURE__*/i.default.createElement(U,null)," Uploading..."):/*#__PURE__*/i.default.createElement("div",{className:"rjf-file-field-input"},/*#__PURE__*/i.default.createElement(ie,s({},r,{inputRef:this.inputRef})))))},t}(i.default.Component);pe.contextType=h;var fe=/*#__PURE__*/function(e){function t(t){var a;return(a=e.call(this,t)||this).handleChange=function(e){a.updateHeight(e.target),a.props.onChange&&a.props.onChange(e)},a.updateHeight=function(e){var t=e.offsetHeight-e.clientHeight;e.style.height="auto",e.style.height=e.scrollHeight+t+"px"},t.inputRef||(a.inputRef=/*#__PURE__*/i.default.createRef()),a}u(t,e);var a=t.prototype;return a.componentDidMount=function(){this.updateHeight(this.props.inputRef?this.props.inputRef.current:this.inputRef.current)},a.render=function(){var e=this.props,t=e.label,a=e.help_text,r=e.error,n=e.inputRef,l=d(e,re);return delete l.type,l.ref=n||this.inputRef,l.onChange=this.handleChange,l.readOnly&&null==l.disabled&&(l.disabled=!0),/*#__PURE__*/i.default.createElement("div",null,/*#__PURE__*/i.default.createElement(ne,{label:t,required:l.required}),/*#__PURE__*/i.default.createElement("div",{className:r?"rjf-input-group has-error":"rjf-input-group"},/*#__PURE__*/i.default.createElement("textarea",l),r&&r.map(function(e,t){/*#__PURE__*/return i.default.createElement("span",{className:"rjf-error-text",key:t},e)}),a&&/*#__PURE__*/i.default.createElement("span",{className:"rjf-help-text"},a)))},t}(i.default.Component),me=/*#__PURE__*/function(e){function t(t){var a;return(a=e.call(this,t)||this).getStateFromProps=function(){var e="",t="12",r="00",n="00",i="000",l="am";if(a.props.value){var o=new Date(a.props.value);e=o.getFullYear().toString().padStart(2,"0")+"-"+(o.getMonth()+1).toString().padStart(2,"0")+"-"+o.getDate().toString().padStart(2,"0"),0===(t=o.getHours())?t=12:12===t?l="pm":t>12&&(t-=12,l="pm"),r=o.getMinutes(),n=o.getSeconds(),i=o.getMilliseconds(),t=t.toString().padStart(2,"0"),r=r.toString().padStart(2,"0"),n=n.toString().padStart(2,"0")}return{date:e,hh:t,mm:r,ss:n,ms:i,ampm:l}},a.handleClickOutside=function(e){a.state.showTimePicker&&(!a.timePickerContainer.current||a.timePickerContainer.current.contains(e.target)||a.timeInput.current.contains(e.target)||a.setState({showTimePicker:!1}))},a.sendValue=function(){var e={target:{type:"text",value:"",name:a.props.name}};if(""===a.state.date||null===a.state.date)return a.props.onChange(e);var t=parseInt(a.state.hh);0===t&&(t=NaN),"am"===a.state.ampm?12===t&&(t=0):"pm"===a.state.ampm&&12!==t&&(t+=12),t=t.toString().padStart(2,"0");var r=a.state.mm.padStart(2,"0"),n=a.state.ss.padStart(2,"0");try{var i=new Date(a.state.date+"T"+t+":"+r+":"+n+"."+a.state.ms);e.target.value=i.toISOString().replace("Z","+00:00")}catch(t){return a.props.onChange(e)}a.props.onChange(e)},a.handleDateChange=function(e){a.setState({date:e.target.value},a.sendValue)},a.handleTimeChange=function(e){a.setState(s({},e),a.sendValue)},a.showTimePicker=function(){a.setState({showTimePicker:!a.props.readOnly&&!0})},a.state=s({},a.getStateFromProps(),{showTimePicker:!1}),a.timeInput=/*#__PURE__*/i.default.createRef(),a.timePickerContainer=/*#__PURE__*/i.default.createRef(),a}u(t,e);var a=t.prototype;return a.componentDidUpdate=function(e,t){if(e.value!==this.props.value&&""!==this.state.hh&&"0"!==this.state.hh&&"00"!==this.state.hh){var a=!1,r=this.getStateFromProps();for(var n in r)if(r[n]!==this.state[n]){a=!0;break}a&&this.setState(s({},r))}},a.componentDidMount=function(){document.addEventListener("mousedown",this.handleClickOutside)},a.componentWillUnmount=function(){document.removeEventListener("mousedown",this.handleClickOutside)},a.render=function(){/*#__PURE__*/return i.default.createElement("div",{className:this.props.error?"rjf-datetime-field has-error":"rjf-datetime-field"},/*#__PURE__*/i.default.createElement(ne,{label:this.props.label,required:this.props.required}),/*#__PURE__*/i.default.createElement("div",{className:"rjf-datetime-field-inner"},/*#__PURE__*/i.default.createElement("div",{className:this.props.readOnly?"rjf-datetime-field-inputs readonly":"rjf-datetime-field-inputs"},/*#__PURE__*/i.default.createElement("div",{className:"rjf-datetime-field-date"},/*#__PURE__*/i.default.createElement(ie,{label:"Date",type:"date",value:this.state.date,onChange:this.handleDateChange,readOnly:this.props.readOnly})),/*#__PURE__*/i.default.createElement("div",{className:"rjf-datetime-field-time"},/*#__PURE__*/i.default.createElement(ie,{label:"Time",type:"text",value:this.state.hh+":"+this.state.mm+":"+this.state.ss+" "+this.state.ampm,onFocus:this.showTimePicker,readOnly:!0,disabled:this.props.readOnly||!1,inputRef:this.timeInput}),/*#__PURE__*/i.default.createElement("div",{ref:this.timePickerContainer},this.state.showTimePicker&&/*#__PURE__*/i.default.createElement(X,{onChange:this.handleTimeChange,hh:this.state.hh,mm:this.state.mm,ss:this.state.ss,ampm:this.state.ampm})))),this.props.error&&this.props.error.map(function(e,t){/*#__PURE__*/return i.default.createElement("span",{className:"rjf-error-text",key:t},e)}),this.props.help_text&&/*#__PURE__*/i.default.createElement("span",{className:"rjf-help-text"},this.props.help_text)))},t}(i.default.Component),he=/*#__PURE__*/function(e){function t(t){var a,r,n;return(a=e.call(this,t)||this).handleSelect=function(e){a.props.multiselect&&(e=Array.isArray(a.props.value)?a.props.value.concat([e]):[e]);var t={target:{type:a.props.type,value:e,name:a.props.name}};a.props.multiselect||a.hideOptions(),a.props.onChange(t)},a.handleMultiselectRemove=function(e){var t=a.props.value.filter(function(t){return t!==e});a.props.onChange({target:{type:a.props.type,value:t,name:a.props.name}})},a.clearValue=function(e){a.handleSelect(a.defaultEmptyValue)},a.hasValue=function(){return!(Array.isArray(a.props.value)&&!a.props.value.length)&&""!==a.props.value&&null!==a.props.value},a.handleSearchInputChange=function(e){var t=e.target.value;t?a.setState({searchInputValue:t,loading:!0},a.debouncedFetchOptions):a.setState({searchInputValue:t,loading:!1,options:[]})},a.fetchOptions=function(){if(""!==a.state.searchInputValue){var e=a.props.handler;if(!e)return console.error("Error: No 'handler' endpoint provided for autocomplete input."),void a.setState({loading:!1});var t=e+"?"+new URLSearchParams({field_name:a.context.fieldName,model_name:a.context.modelName,coords:j(a.props.name),query:a.state.searchInputValue});fetch(t,{method:"GET"}).then(function(e){return e.json()}).then(function(e){Array.isArray(e.results)||(e.results=[]),a.setState(function(t){return{loading:!1,options:[].concat(e.results)}})}).catch(function(e){alert("Something went wrong while fetching options"),console.error("Error:",e),a.setState({loading:!1})})}},a.showOptions=function(e){a.state.showOptions||a.setState({showOptions:!0})},a.hideOptions=function(e){a.setState({showOptions:!1,searchInputValue:"",options:[],loading:!1})},a.toggleOptions=function(e){a.setState(function(e){return e.showOptions?{showOptions:!1,searchInputValue:"",options:[],loading:!1}:{showOptions:!0}})},a.state={searchInputValue:"",showOptions:!1,options:[],loading:!1},a.optionsContainer=/*#__PURE__*/i.default.createRef(),a.searchInputRef=/*#__PURE__*/i.default.createRef(),a.input=/*#__PURE__*/i.default.createRef(),a.debouncedFetchOptions=(r=a.fetchOptions,function(){clearTimeout(n);var e=arguments,t=this;n=setTimeout(function(){r.apply(t,e)},500)}),a.defaultEmptyValue=t.multiselect?[]:"",a}u(t,e);var a=t.prototype;return a.componentDidUpdate=function(e,t){this.state.showOptions&&this.state.showOptions!==t.showOptions&&this.searchInputRef.current&&this.searchInputRef.current.focus()},a.render=function(){var e=this;/*#__PURE__*/return i.default.createElement("div",{className:this.props.label?"rjf-autocomplete-field has-label":"rjf-autocomplete-field"},this.props.multiselect?/*#__PURE__*/i.default.createElement(ie,{label:this.props.label,help_text:this.props.help_text,error:this.props.error},/*#__PURE__*/i.default.createElement(ce,{inputRef:this.input,onClick:this.toggleOptions,onChange:function(t){return e.handleMultiselectRemove(t.target.value)},value:this.props.value,placeholder:this.props.placeholder||" ",disabled:this.props.readOnly||!1})):/*#__PURE__*/i.default.createElement(i.default.Fragment,null,/*#__PURE__*/i.default.createElement(ie,{label:this.props.label,type:"text",value:this.props.value,help_text:this.props.help_text,error:this.props.error,readOnly:!0,disabled:this.props.readOnly||!1,onClick:this.toggleOptions,inputRef:this.input,placeholder:this.props.placeholder,name:this.props.name,className:"rjf-autocomplete-field-input"}),this.hasValue()&&!this.props.readOnly&&/*#__PURE__*/i.default.createElement(H,{className:"autocomplete-field-clear",title:"Clear",onClick:this.clearValue},/*#__PURE__*/i.default.createElement(J,{name:"x-circle"})," ",/*#__PURE__*/i.default.createElement("span",null,"Clear"))),this.state.showOptions&&!this.props.readOnly&&/*#__PURE__*/i.default.createElement(ve,{options:this.state.options,value:this.props.value,hideOptions:this.hideOptions,onSelect:this.handleSelect,onSearchInputChange:this.handleSearchInputChange,searchInputValue:this.state.searchInputValue,containerRef:this.optionsContainer,searchInputRef:this.searchInputRef,inputRef:this.input,loading:this.state.loading,hasHelpText:(this.props.help_text||this.props.error)&&1,multiselect:this.props.multiselect}))},t}(i.default.Component);he.contextType=h;var ve=/*#__PURE__*/function(e){function t(){for(var t,a=arguments.length,r=new Array(a),n=0;n<a;n++)r[n]=arguments[n];return(t=e.call.apply(e,[this].concat(r))||this).handleClickOutside=function(e){!t.props.containerRef.current||t.props.containerRef.current.contains(e.target)||t.props.inputRef.current.contains(e.target)||t.props.hideOptions()},t}u(t,e);var a=t.prototype;return a.componentDidMount=function(){document.addEventListener("mousedown",this.handleClickOutside)},a.componentWillUnmount=function(){document.removeEventListener("mousedown",this.handleClickOutside)},a.render=function(){/*#__PURE__*/return i.default.createElement("div",{ref:this.props.containerRef},/*#__PURE__*/i.default.createElement("div",{className:"rjf-autocomplete-field-popup",style:this.props.hasHelpText?{marginTop:"-15px"}:{}},/*#__PURE__*/i.default.createElement(ge,{inputRef:this.props.searchInputRef,onChange:this.props.onSearchInputChange,value:this.props.searchInputValue,loading:this.props.loading}),this.props.searchInputValue&&/*#__PURE__*/i.default.createElement(ye,{options:this.props.options,value:this.props.value,onSelect:this.props.onSelect,loading:this.props.loading,hasHelpText:this.props.hasHelpText,multiselect:this.props.multiselect})))},t}(i.default.Component);function ge(e){/*#__PURE__*/return i.default.createElement("div",{className:"rjf-autocomplete-field-search"},/*#__PURE__*/i.default.createElement(ie,{type:"text",placeholder:"Search...",inputRef:e.inputRef,onChange:e.onChange,value:e.value,form:""}),e.loading&&/*#__PURE__*/i.default.createElement(U,null))}function ye(e){/*#__PURE__*/return i.default.createElement("div",{className:"rjf-autocomplete-field-options"},!e.options.length&&!e.loading&&/*#__PURE__*/i.default.createElement("div",{className:"rjf-autocomplete-field-option disabled"},"No options"),e.options.map(function(t,a){var r,n;"object"==typeof t?(r=t.title||t.label,n=t.value):("boolean"==typeof(r=t)&&(r=capitalize(r.toString())),n=t);var l,o="rjf-autocomplete-field-option";return(l=Array.isArray(e.value)?e.value.indexOf(n)>-1:e.value===n)&&(o+=" selected"),/*#__PURE__*/i.default.createElement("div",{key:r+"_"+n+"_"+a,className:o,tabIndex:0,role:"button",onClick:function(){return e.multiselect&&l?null:e.onSelect(n)}},r)}))}function Oe(e){return e.children?/*#__PURE__*/i.default.createElement("div",{className:"rjf-form-group-title"},e.children,e.editable&&/*#__PURE__*/i.default.createElement(i.default.Fragment,null," ",/*#__PURE__*/i.default.createElement(H,{className:"edit",onClick:e.onEdit,title:"Edit"},"Edit")),e.collapsible&&/*#__PURE__*/i.default.createElement(i.default.Fragment,null," ",/*#__PURE__*/i.default.createElement(H,{className:"collapse",onClick:e.onCollapse,title:e.collapsed?"Expand":"Collapse"},e.collapsed?"[+]":"[-]"))):null}function Ee(e){return e.children?/*#__PURE__*/i.default.createElement("div",{className:"rjf-form-group-description"},e.children):null}function be(e,t,a){var r=e.target.parentElement.parentElement,n=r.previousElementSibling,i=r.nextElementSibling;if(r.classList.add("rjf-animate","rjf-"+t),"move-up"===t){var l=n.getBoundingClientRect().y,o=l,s=l=r.getBoundingClientRect().y;n.classList.add("rjf-animate"),n.style.opacity=0,n.style.transform="translateY("+(s-o)+"px)",r.style.opacity=0,r.style.transform="translateY(-"+(s-o)+"px)"}else if("move-down"===t){var u=r.getBoundingClientRect().y,c=u,d=u=i.getBoundingClientRect().y;i.classList.add("rjf-animate"),i.style.opacity=0,i.style.transform="translateY(-"+(d-c)+"px)",r.style.opacity=0,r.style.transform="translateY("+(d-c)+"px)"}setTimeout(function(){a(),r.classList.remove("rjf-animate","rjf-"+t),r.style=null,"move-up"===t?(n.classList.remove("rjf-animate"),n.style=null):"move-down"===t&&(i.classList.remove("rjf-animate"),i.style=null)},200)}function we(e){/*#__PURE__*/return i.default.createElement("div",{className:"rjf-form-row-controls"},e.onMoveUp&&/*#__PURE__*/i.default.createElement(H,{className:"move-up",onClick:function(t){return be(t,"move-up",e.onMoveUp)},title:"Move up"},/*#__PURE__*/i.default.createElement("span",null,"↑")),e.onMoveDown&&/*#__PURE__*/i.default.createElement(H,{className:"move-down",onClick:function(t){return be(t,"move-down",e.onMoveDown)},title:"Move down"},/*#__PURE__*/i.default.createElement("span",null,"↓")),e.onRemove&&/*#__PURE__*/i.default.createElement(H,{className:"remove",onClick:function(t){return be(t,"remove",e.onRemove)},title:"Remove"},/*#__PURE__*/i.default.createElement("span",null,"×")))}function je(e){var t="rjf-form-row";return e.hidden&&(t+=" rjf-form-row-hidden"),/*#__PURE__*/i.default.createElement("div",{className:t},/*#__PURE__*/i.default.createElement(we,e),/*#__PURE__*/i.default.createElement("div",{className:"rjf-form-row-inner"},e.children))}function ke(e){var t=i.default.useState(!1),a=t[0],r=t[1],n=O(e.schema);i.default.Children.count(e.children);var l,o,s=0===e.level&&"groups"===e.childrenType?"":"rjf-form-group-inner";return"object"===n?(l="Add key",o="Add new key"):(l="Add item",o="Add new item"),/*#__PURE__*/i.default.createElement("div",{className:"rjf-form-group"},0===e.level&&/*#__PURE__*/i.default.createElement(Oe,{editable:e.editable,onEdit:e.onEdit,collapsible:e.collapsible,onCollapse:function(){return r(!a)},collapsed:a},e.schema.title),0===e.level&&/*#__PURE__*/i.default.createElement(Ee,null,e.schema.description),/*#__PURE__*/i.default.createElement("div",{className:s},e.level>0&&/*#__PURE__*/i.default.createElement(Oe,{editable:e.editable,onEdit:e.onEdit,collapsible:e.collapsible,onCollapse:function(){return r(!a)},collapsed:a},e.schema.title),e.level>0&&/*#__PURE__*/i.default.createElement(Ee,null,e.schema.description),a&&/*#__PURE__*/i.default.createElement("div",{className:"rjf-collapsed-indicator"},/*#__PURE__*/i.default.createElement("span",null,"Collapsed")),/*#__PURE__*/i.default.createElement("div",{className:a?"rjf-form-group-children rjf-collapsed":"rjf-form-group-children"},e.children),!a&&e.addable&&/*#__PURE__*/i.default.createElement(H,{className:"add",onClick:function(t){return e.onAdd()},title:o},l)))}var Se=/*#__PURE__*/function(e){function t(t){var a;return(a=e.call(this,t)||this).openModal=function(e){a.setState({open:!0})},a.closeModal=function(e){a.setState({open:!1,pane:"upload"})},a.togglePane=function(e){a.setState({pane:e})},a.handleFileSelect=function(e){a.props.onChange({target:{type:"text",value:e,name:a.props.name}}),a.closeModal()},a.handleFileUpload=function(e){a.newFiles.push(e.target.value),a.addExitEventListeners(),a.props.onChange(e),a.closeModal()},a.addExitEventListeners=function(){a.exitListenersAdded||a.hiddenInputRef.current&&a.hiddenInputRef.current.form&&(window.addEventListener("beforeunload",a.promptOnExit),window.addEventListener("unload",a.sendDeleteRequestOnExit),a.hiddenInputRef.current.form.addEventListener("submit",function(e){window.removeEventListener("beforeunload",a.promptOnExit),window.removeEventListener("unload",a.sendDeleteRequestOnExit)}),a.exitListenersAdded=!0)},a.promptOnExit=function(e){a.newFiles.length&&(e.preventDefault(),e.returnValue="")},a.sendDeleteRequestOnExit=function(e){a.newFiles.length&&a.sendDeleteRequest([a.newFiles],"unsaved_form_page_exit",!0)},a.clearFile=function(){window.confirm("Do you want to remove this file?")&&a.props.onChange({target:{type:"text",value:"",name:a.props.name}})},a.sendDeleteRequest=function(e,t,r){for(var n=a.props.handler||a.context.fileHandler,i=new URLSearchParams(s({},a.context.fileHandlerArgs,{coords:j(a.props.name),trigger:t})),l=0;l<e.length;l++)i.append("value",e[l]);var o=n+"?"+i,u={method:"DELETE",headers:{"X-CSRFToken":E()}};return r&&(u.keepalive=!0),fetch(o,u)},a.state={value:t.value,loading:!1,open:!1,pane:"upload"},a.hiddenInputRef=/*#__PURE__*/i.default.createRef(),a.newFiles=[],a.exitListenersAdded=!1,a}return u(t,e),t.prototype.render=function(){return this.props.handler||this.context.fileHandler?/*#__PURE__*/i.default.createElement("div",null,/*#__PURE__*/i.default.createElement(ne,{label:this.props.label,required:this.props.required}),/*#__PURE__*/i.default.createElement("div",{className:"rjf-file-field"},this.props.value&&/*#__PURE__*/i.default.createElement("div",{className:"rjf-current-file-name"},"Current file: ",/*#__PURE__*/i.default.createElement("span",null,this.props.value)," "," ",/*#__PURE__*/i.default.createElement(H,{className:"remove-file",onClick:this.clearFile},"Clear")),/*#__PURE__*/i.default.createElement(H,{onClick:this.openModal,className:"upload-modal__open"},this.props.value?"Change file":"Select file"),this.props.error&&this.props.error.map(function(e,t){/*#__PURE__*/return i.default.createElement("span",{className:"rjf-error-text",key:t},e)}),this.props.help_text&&/*#__PURE__*/i.default.createElement("span",{className:"rjf-help-text"},this.props.help_text)),/*#__PURE__*/i.default.createElement("input",{type:"hidden",ref:this.hiddenInputRef}),/*#__PURE__*/i.default.createElement(l.default,{isOpen:this.state.open,onRequestClose:this.closeModal,contentLabel:"Select file",portalClassName:"rjf-modal-portal",overlayClassName:"rjf-modal__overlay",className:"rjf-modal__dialog",bodyOpenClassName:"rjf-modal__main-body--open",closeTimeoutMS:150,ariaHideApp:!1},/*#__PURE__*/i.default.createElement("div",{className:"rjf-modal__content"},/*#__PURE__*/i.default.createElement("div",{className:"rjf-modal__header"},/*#__PURE__*/i.default.createElement(Ne,{onClick:this.togglePane,tabName:"upload",active:"upload"===this.state.pane},"Upload new")," ",/*#__PURE__*/i.default.createElement(Ne,{onClick:this.togglePane,tabName:"library",active:"library"===this.state.pane},"Choose from library"),/*#__PURE__*/i.default.createElement(H,{className:"modal__close",onClick:this.closeModal,title:"Close (Esc)"},/*#__PURE__*/i.default.createElement(J,{name:"x-lg"}))),/*#__PURE__*/i.default.createElement("div",{className:"rjf-modal__body"},"upload"===this.state.pane&&/*#__PURE__*/i.default.createElement(Ce,s({},this.props,{onChange:this.handleFileUpload,label:"",value:"",help_text:"",error:""})),"library"===this.state.pane&&/*#__PURE__*/i.default.createElement(xe,{fileHandler:this.props.handler||this.context.fileHandler,fileHandlerArgs:s({},this.context.fileHandlerArgs,{coords:j(this.props.name)}),onFileSelect:this.handleFileSelect,sendDeleteRequest:this.sendDeleteRequest})),/*#__PURE__*/i.default.createElement("div",{className:"rjf-modal__footer"},/*#__PURE__*/i.default.createElement(H,{className:"modal__footer-close",onClick:this.closeModal},"Cancel"))))):/*#__PURE__*/i.default.createElement(pe,this.props)},t}(i.default.Component);function Ne(e){var t="rjf-upload-modal__tab-button";return e.active&&(t+=" rjf-upload-modal__tab-button--active"),/*#__PURE__*/i.default.createElement("button",{onClick:function(){return e.onClick(e.tabName)},className:t},e.children)}function Ce(e){/*#__PURE__*/return i.default.createElement("div",{className:"rjf-upload-modal__pane"},/*#__PURE__*/i.default.createElement("h3",null,"Upload new"),/*#__PURE__*/i.default.createElement("br",null),/*#__PURE__*/i.default.createElement(pe,e))}Se.contextType=h;var xe=/*#__PURE__*/function(e){function t(t){var a;return(a=e.call(this,t)||this).fetchList=function(){var e=a.props.fileHandler;if(!e)return console.error("Error: fileHandler option need to be passed while initializing editor for enabling file listing."),void a.setState({loading:!1,hasMore:!1});var t=e+"?"+new URLSearchParams(s({},a.props.fileHandlerArgs,{page:a.state.page+1}));fetch(t,{method:"GET"}).then(function(e){return e.json()}).then(function(e){Array.isArray(e.results)||(e.results=[]),a.setState(function(t){return{loading:!1,files:[].concat(t.files,e.results),page:e.results.length>0?t.page+1:t.page,hasMore:e.results.length>0}})}).catch(function(e){alert("Something went wrong while retrieving media files"),console.error("Error:",e),a.setState({loading:!1})})},a.onLoadMore=function(e){a.setState({loading:!0},a.fetchList)},a.onFileDelete=function(){a.setState({page:0,files:[]},a.onLoadMore)},a.state={loading:!0,files:[],page:0,hasMore:!0},a}u(t,e);var a=t.prototype;return a.componentDidMount=function(){this.fetchList()},a.render=function(){var e=this;/*#__PURE__*/return i.default.createElement("div",{className:"rjf-upload-modal__pane"},/*#__PURE__*/i.default.createElement("h3",null,"Media library"),/*#__PURE__*/i.default.createElement("div",{className:"rjf-upload-modal__media-container"},this.state.files.map(function(t){/*#__PURE__*/return i.default.createElement(Pe,s({},t,{onClick:e.props.onFileSelect,sendDeleteRequest:e.props.sendDeleteRequest,onFileDelete:e.onFileDelete}))})),this.state.loading&&/*#__PURE__*/i.default.createElement(U,{className:"rjf-upload-modal__media-loader"}),!this.state.loading&&this.state.hasMore&&/*#__PURE__*/i.default.createElement("div",null,/*#__PURE__*/i.default.createElement(H,{onClick:this.onLoadMore,className:"upload-modal__media-load"},/*#__PURE__*/i.default.createElement(J,{name:"arrow-down"})," View more")),!this.state.hasMore&&/*#__PURE__*/i.default.createElement("div",{className:"rjf-upload-modal__media-end-message"},this.state.files.length?"End of list":"No files found"))},t}(i.default.Component);function Pe(e){var t=e.metadata||{};/*#__PURE__*/return i.default.createElement("div",{className:"rjf-upload-modal__media-tile"},/*#__PURE__*/i.default.createElement(Ae,{value:e.value,sendDeleteRequest:e.sendDeleteRequest,onFileDelete:e.onFileDelete}),/*#__PURE__*/i.default.createElement("div",{className:"rjf-upload-modal__media-tile-inner",tabIndex:"0",onClick:function(){return e.onClick(e.value)}},/*#__PURE__*/i.default.createElement("img",{src:e.thumbnail?e.thumbnail:"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' fill='%23999999' viewBox='0 0 16 16'%3E%3Cpath d='M14 4.5V14a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V2a2 2 0 0 1 2-2h5.5L14 4.5zm-3 0A1.5 1.5 0 0 1 9.5 3V1H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1V4.5h-2z'/%3E%3C/svg%3E"}),e.metadata&&/*#__PURE__*/i.default.createElement("div",{className:"rjf-upload-modal__media-tile-metadata"},Object.getOwnPropertyNames(t).map(function(e){/*#__PURE__*/return i.default.createElement("span",null,t[e])}))))}var Ae=/*#__PURE__*/function(e){function t(t){var a;return(a=e.call(this,t)||this).toggleMenu=function(e){a.setState(function(e){return{open:!e.open}})},a.handleDeleteClick=function(e){window.confirm("Do you want to delete this file?")&&(a.setState({loading:!0}),a.props.sendDeleteRequest([a.props.value],"delete_button").then(function(e){var t,r=e.status;200===r||(400===r?t="Bad request":401===r||403===r?t="You don't have permission to delete this file":404===r?t="This file does not exist on server":405===r?t="This operation is not permitted":r>405&&(t="Something went wrong while deleting file")),a.setState({loading:!1,open:!1}),t?alert(t):a.props.onFileDelete()}).catch(function(e){alert("Something went wrong while deleting file"),console.error("Error:",e),a.setState({loading:!1})}))},a.state={open:!1,loading:!1},a}return u(t,e),t.prototype.render=function(){/*#__PURE__*/return i.default.createElement("div",{className:this.state.open?"rjf-dropdown open":"rjf-dropdown"},/*#__PURE__*/i.default.createElement(H,{className:"rjf-dropdown-toggler",alterClassName:!1,title:this.state.open?"Close menu":"Open menu",onClick:this.toggleMenu},/*#__PURE__*/i.default.createElement(J,{name:this.state.open?"x-lg":"three-dots-vertical"})),this.state.open&&/*#__PURE__*/i.default.createElement("div",{className:"rjf-dropdown-items"},/*#__PURE__*/i.default.createElement(H,{className:"rjf-dropdown-item rjf-text-danger",alterClassName:!1,onClick:this.handleDeleteClick},this.state.loading&&/*#__PURE__*/i.default.createElement(U,null),this.state.loading?" Deleting...":"Delete")))},t}(i.default.Component),Re=["data","schema","name","onChange","onRemove","removable","onEdit","onKeyEdit","editable","onMoveUp","onMoveDown","parentType","errorMap"];function _e(e){var t,a={name:e.name,value:e.data,readOnly:S(e.schema,"readOnly","readonly"),help_text:S(e.schema,"help_text","helpText"),error:e.errorMap[j(e.name)],required:e.schema.required||!1};"string"==typeof a.error&&(a.error=[a.error]),e.schema.placeholder&&(a.placeholder=e.schema.placeholder),e.schema.handler&&(a.handler=e.schema.handler),e.schema.hasOwnProperty("const")?(t=y(e.schema.const),a.readOnly=!0):t=k(e.schema.type);var r,n=S(e.schema,"choices","enum");switch(n&&(a.options=n,t="select"),e.schema.widget&&("multiselect"===e.schema.widget&&"array"!==e.parentType||(t="hidden"===e.schema.widget?"string":e.schema.widget)),t){case"string":r=ie,e.schema.format?("data-url"===e.schema.format?r=pe:"file-url"===e.schema.format?r=Se:"date-time"===k(e.schema.format)&&(r=me),a.type=e.schema.format):a.type="hidden"===e.schema.widget?"hidden":"text",(e.schema.minLength||0===e.schema.minLength)&&(a.minLength=e.schema.minLength),(e.schema.maxLength||0===e.schema.maxLength)&&(a.maxLength=e.schema.maxLength);break;case"fileinput":r=pe,e.schema.format&&(a.type=e.schema.format);break;case"range":case"integer":a.step="1";case"number":a.type="range"===t?"range":"number",r=ie,(e.schema.minimum||0===e.schema.minimum)&&(a.min=e.schema.minimum),(e.schema.maximum||0===e.schema.maximum)&&(a.max=e.schema.maximum);break;case"boolean":case"checkbox":a.type="checkbox",r=le;break;case"radio":a.type="radio",r=oe;break;case"select":r=se;break;case"multiselect":a.valueType=e.schema.type,r=ue;break;case"autocomplete":r=he;break;case"multiselect-autocomplete":r=he,a.multiselect=!0;break;case"textarea":r=fe,(e.schema.minLength||0===e.schema.minLength)&&(a.minLength=e.schema.minLength),(e.schema.maxLength||0===e.schema.maxLength)&&(a.maxLength=e.schema.maxLength);break;default:a.type="text",r=ie}/*#__PURE__*/return i.default.createElement(r,s({},a,{label:e.editable?/*#__PURE__*/i.default.createElement("span",null,e.schema.title," ",/*#__PURE__*/i.default.createElement(H,{className:"edit",onClick:e.onEdit,title:"Edit"},"Edit")):e.schema.title,onChange:function(t){return function(e,t,a){var r;r="checkbox"===e.target.type?e.target.checked:e.target.value,r=Array.isArray(r)?r.map(function(e){return g(e,t)}):g(r,t),a(e.target.name,r)}(t,e.schema.type,e.onChange)}}))}function Me(e){var t=e.data,a=e.schema,r=e.name,n=e.onChange,l=e.onRemove,o=e.removable,u=e.onKeyEdit,c=e.editable,p=e.onMoveUp,f=e.onMoveDown,m=e.parentType,h=e.errorMap,v=d(e,Re);/*#__PURE__*/return i.default.createElement(je,{key:r,onRemove:o?function(e){return l(r)}:null,onMoveUp:p,onMoveDown:f,hidden:"hidden"===a.widget},/*#__PURE__*/i.default.createElement(_e,s({data:t,schema:a,name:r,onChange:n,onEdit:u,editable:c,parentType:m,errorMap:h},v)))}function Ie(e){var t=e.data,a=e.schema,r=e.name,n=e.onChange,l=e.onAdd,o=e.onRemove,u=e.onMove,c=e.onEdit,d=e.level,p=[],f=[],m=S(a,"readonly","readOnly",!1),h=!0,v=S(a,"min_items","minItems")||0;(t.length<=v||m)&&(h=!1);var g=!0,y=S(a,"max_items","maxItems")||100;(t.length>=y||m)&&(g=!1),a.items.hasOwnProperty("$ref")&&(a.items=s({},e.getRef(a.items.$ref),a.items),delete a.items.$ref);var O=k(a.items.type),E={schema:a.items,onChange:n,onAdd:l,onRemove:o,level:d+1,removable:h,onMove:u,onEdit:c,onKeyEdit:e.onKeyEdit,parentType:"array",getRef:e.getRef,errorMap:e.errorMap};if(m&&(E.schema.readOnly=!0),"multiselect"===E.schema.widget||"multiselect-autocomplete"===E.schema.widget)E.data=t,E.name=r,E.removable=!1,E.onMoveUp=null,E.onMoveDown=null,g=!1,p.push(Me(E));else for(var w=function(n){E.data=t[n],E.name=b(r,n),E.onMoveUp=0===n||m?null:function(e){return u(b(r,n),b(r,n-1))},E.onMoveDown=n===t.length-1||m?null:function(e){return u(b(r,n),b(r,n+1))},"array"===O?f.push(Ie(E)):"object"===O?f.push(De(E)):a.items.hasOwnProperty("oneOf")?f.push(/*#__PURE__*/i.default.createElement(Fe,{parentArgs:e,nextArgs:s({},E),key:"oneOf_"+r+"_"+n})):a.items.hasOwnProperty("anyOf")?f.push(/*#__PURE__*/i.default.createElement(qe,{parentArgs:e,nextArgs:s({},E),key:"anyOf_"+r+"_"+n})):p.push(Me(E))},N=0;N<t.length;N++)w(N);var C,x=r;if((p.length||!p.length&&!f.length)&&(f.length||"string"==typeof(C=e.errorMap[j(x)])&&(C=[C]),p=/*#__PURE__*/i.default.createElement(ke,{level:d,schema:a,addable:g,onAdd:function(){return l(R(a.items,e.getRef),x)},editable:e.editable,onEdit:e.onKeyEdit,key:"row_group_"+r,collapsible:t.length>0,childrenType:"rows"},C&&C.map(function(e,t){/*#__PURE__*/return i.default.createElement("div",{className:"rjf-error-text",key:t},e)}),p),"object"===e.parentType&&e.removable&&(p=/*#__PURE__*/i.default.createElement("div",{className:"rjf-form-group-wrapper",key:"row_group_wrapper_"+r},/*#__PURE__*/i.default.createElement(we,{onRemove:function(e){return o(r)}}),p))),f.length){var P=e.errorMap[j(x)];"string"==typeof P&&(P=[P]),f=/*#__PURE__*/i.default.createElement("div",{key:"group_"+r,className:"rjf-form-group-wrapper"},"object"===e.parentType&&e.removable&&/*#__PURE__*/i.default.createElement(we,{onRemove:function(e){return o(r)}}),/*#__PURE__*/i.default.createElement(ke,{level:d,schema:a,addable:g,onAdd:function(){return l(R(a.items,e.getRef),x)},editable:e.editable,onEdit:e.onKeyEdit,collapsible:t.length>0,childrenType:"groups"},P&&P.map(function(e,t){/*#__PURE__*/return i.default.createElement("div",{className:"rjf-error-text",key:t},e)}),f.map(function(e,t){/*#__PURE__*/return i.default.createElement("div",{className:"rjf-form-group-wrapper",key:"group_wrapper_"+r+"_"+t},/*#__PURE__*/i.default.createElement(we,{onRemove:h?function(e){return o(b(r,t))}:null,onMoveUp:t>0&&!m?function(e){return u(b(r,t),b(r,t-1))}:null,onMoveDown:t<f.length-1&&!m?function(e){return u(b(r,t),b(r,t+1))}:null}),e)})))}return[].concat(p,f)}function De(e){var t=e.data,a=e.schema,r=e.name,n=e.onChange,l=e.onAdd,o=e.onRemove,u=e.onMove,c=e.onEdit,d=e.level,p=[],f=S(a,"readonly","readOnly",!1),m=S(a,"keys","properties",{});if(a.hasOwnProperty("allOf"))for(var h=0;h<a.allOf.length;h++)m=s({},m,S(a.allOf[h],"keys","properties",{}));var g=[].concat(Object.keys(m));a.additionalProperties&&(g=[].concat(g,Object.keys(t).filter(function(e){return-1===g.indexOf(e)})));for(var y=function(h){var y=g[h],O=t[y],E=b(r,y),j=m.hasOwnProperty(y)?s({},m[y]):void 0,S=!m.hasOwnProperty(y);void 0===j&&(j="boolean"==typeof a.additionalProperties?{type:"string"}:s({},a.additionalProperties)),j.hasOwnProperty("$ref")&&delete(j=s({},e.getRef(j.$ref),j)).$ref,f&&(j.readOnly=!0);var N=k(j.type);j.title||(j.title=S?y:function(e){return null==e?"":v(e=e.replace(/_/g," "))}(y));var C=!1;void 0===m[y]&&(C=!0),a.hasOwnProperty("required")&&Array.isArray(a.required)&&a.required.indexOf(y)>-1&&(j.required=!0);var x={data:O,schema:j,name:E,onChange:n,onAdd:l,onRemove:o,level:d+1,removable:C,onMove:u,onEdit:c,parentType:"object",getRef:e.getRef,errorMap:e.errorMap,onKeyEdit:function(){return function(e,t,a,r,n){var i=prompt("Rename key",t);if(null!==i&&(i=i.trim())!==t){if(!i)return alert("(!) Key name can't be empty.\r\n\r\n‎");if(e.hasOwnProperty(i))return alert("(!) Duplicate keys not allowed. This key already exists.\r\n\r\n‎");var l=w(r);l.pop(),l.push(i),n(a,l=b.apply(null,l),r)}}(t,y,O,E,c)}};x.editable=C,"array"===N?p.push(Ie(x)):"object"===N?p.push(De(x)):x.schema.hasOwnProperty("oneOf")?p.push(/*#__PURE__*/i.default.createElement(Fe,{parentArgs:e,nextArgs:s({},x),key:"oneOf_"+r+"_"+h})):x.schema.hasOwnProperty("anyOf")?p.push(/*#__PURE__*/i.default.createElement(qe,{parentArgs:e,nextArgs:s({},x),key:"anyOf_"+r+"_"+h})):p.push(Me(x))},O=0;O<g.length;O++)y(O);if(a.hasOwnProperty("oneOf")&&p.push(/*#__PURE__*/i.default.createElement(Fe,{parentArgs:e,key:"oneOf_"+r})),a.hasOwnProperty("anyOf")&&p.push(/*#__PURE__*/i.default.createElement(qe,{parentArgs:e,key:"anyOf_"+r})),p.length||a.additionalProperties){var E=r,N=e.errorMap[j(E)];"string"==typeof N&&(N=[N]),p=/*#__PURE__*/i.default.createElement(ke,{level:d,schema:a,addable:a.additionalProperties&&!f,onAdd:function(){return function(e,t,a,r,n){var i=prompt("Add new key");null!==i&&(!0===r&&(r={type:"string"}),(i=i.trim())?e.hasOwnProperty(i)?alert("(!) Duplicate keys not allowed. This key already exists.\r\n\r\n‎"):a(R(r,n),b(t,i)):alert("(!) Can't add empty key.\r\n\r\n‎"))}(t,E,l,a.additionalProperties,e.getRef)},editable:e.editable,onEdit:e.onKeyEdit,key:"row_group_"+r,collapsible:g.length>0,childrenType:"rows"},N&&N.map(function(e,t){/*#__PURE__*/return i.default.createElement("div",{className:"rjf-error-text",key:t},e)}),p),"object"===e.parentType&&e.removable&&(p=/*#__PURE__*/i.default.createElement("div",{className:"rjf-form-group-wrapper",key:"row_group_wrapper_"+r},/*#__PURE__*/i.default.createElement(we,{onRemove:function(e){return o(r)}}),p))}return p}function Ve(e){/*#__PURE__*/return i.default.createElement($e,{args:e})}function Te(e){/*#__PURE__*/return i.default.createElement($e,{args:e,schemaName:"anyOf"})}function Le(e){return De(e)}var $e=/*#__PURE__*/function(e){function t(t){var a;return(a=e.call(this,t)||this).findSelectedOption=function(){return y(a.props.args.data),L(a.props.args.data,a.props.args.schema,a.props.args.getRef,a.schemaName)},a.getOptions=function(){return a.props.args.schema[a.schemaName].map(function(e,t){return{label:e.title||"Option "+(t+1),value:t}})},a.getSchema=function(e){void 0===e&&(e=a.state.option);var t=a.props.args.schema[a.schemaName][e];return t.hasOwnProperty("$ref")&&delete(t=s({},a.props.args.getRef(t.$ref),{schema:t})).$ref,t},a.handleOptionChange=function(e){a.updateData(a.getSchema(e.target.value))},a.schemaName=a.props.schemaName||"oneOf",a}u(t,e);var a=t.prototype;return a.updateData=function(e){this.props.args.onChange(this.props.args.name,R(e,this.props.args.getRef))},a.render=function(){var e,t=this.findSelectedOption(),a=this.getSchema(t),r=O(a),n=this.props.args;"object"===r?e=De:"array"===r?e=Ie:(e=Me,n.removable=!1,n.onMoveUp=null,n.onMoveDown=null,(Array.isArray(n.data)||"object"==typeof n.data)&&(n.data=null));var l=e(s({},n,{schema:a})),o=this.props.args.schema.title||null;/*#__PURE__*/return i.default.createElement("div",{className:"rjf-form-group rjf-oneof-group rjf-oneof-group-top-level"},/*#__PURE__*/i.default.createElement("div",{className:"rjf-oneof-selector"},/*#__PURE__*/i.default.createElement(se,{value:t,options:this.getOptions(),onChange:this.handleOptionChange,className:"rjf-oneof-selector-input",label:o})),l)},t}(i.default.Component),Fe=/*#__PURE__*/function(e){function t(t){var a;return(a=e.call(this,t)||this).findSelectedOption=function(){var e=0;if(a.props.nextArgs)for(var t=y(a.props.nextArgs.data),r=a.props.nextArgs.schema[a.schemaName],n=0;n<r.length;n++){var i=r[n];i.hasOwnProperty("$ref")&&delete(i=s({},a.props.parentArgs.getRef(i.$ref),i)).$ref;var l=O(i);if(i.hasOwnProperty("const")){if(i.const===a.props.nextArgs.data){e=1;break}}else if("number"===t){if("number"===l||"integer"===l){e=n;break}}else{if("null"===t&&["boolean","integer","number"].indexOf(l)>-1){e=n;break}if("object"===t){if($(a.props.nextArgs.data,i)){e=n;break}}else if("array"===t){if(F(a.props.nextArgs.data,i)){e=n;break}}else if(t===l){e=n;break}}}else{var o=a.props.parentArgs.data,u=y(o),c=a.props.parentArgs.schema[a.schemaName];if(void 0===c)return e;for(var d=0;d<c.length;d++){var p=c[d];if(O(p)===u)if("object"===u){if($(o,p)){e=d;break}}else if("array"===u)throw new Error("Unexpected block (#1) tirggered. If you see this error, you've found a rare schema. Please report this issue on our Github.")}}return e},a.getOptions=function(){var e=a.getParentType();return"object"===e?(a.props.nextArgs?a.props.nextArgs.schema:a.props.parentArgs.schema)[a.schemaName].map(function(e,t){return{label:e.title||"Option "+(t+1),value:t}}):"array"===e?a.props.parentArgs.schema.items[a.schemaName].map(function(e,t){return{label:e.title||"Option "+(t+1),value:t}}):[]},a.getSchema=function(e){void 0===e&&(e=a.state.option);var t,r=a.getParentType();return"object"===r?a.props.nextArgs?(t=s({},a.props.nextArgs.schema[a.schemaName][e])).title||(t.title=a.props.nextArgs.schema.title):t=a.props.parentArgs.schema[a.schemaName][e]:t="array"===r?a.props.parentArgs.schema.items[a.schemaName][e]:{type:"string"},t.hasOwnProperty("$ref")&&delete(t=s({},a.props.parentArgs.getRef(t.$ref),t)).$ref,t},a.getParentType=function(){return O(a.props.parentArgs.schema)},a.handleOptionChange=function(e,t){a.updateData(a.getSchema(t),a.getSchema(e.target.value))},a.schemaName=a.props.schemaName||"oneOf",a}u(t,e);var a=t.prototype;return a.updateData=function(e,t){var a=this,r=this.getParentType();if("object"!==r||this.props.nextArgs)("array"===r||this.props.nextArgs)&&this.props.parentArgs.onChange(this.props.nextArgs.name,R(t,this.props.parentArgs.getRef));else{var n=this.props.parentArgs.name,i=t,l=this.props.parentArgs.data,o=S(i,"properties","keys",{}),s=[].concat(Object.keys(S(e,"properties","keys"))),u=[].concat(Object.keys(S(i,"properties","keys"))),c={};for(var d in l)l.hasOwnProperty(d)&&(s.indexOf(d)>-1||(c[d]=l[d]));u.forEach(function(e,t){c[e]=R(o[e],a.props.parentArgs.getRef)}),this.props.parentArgs.onChange(n,c)}},a.render=function(){var e,t=this,a=this.findSelectedOption(),r=this.getSchema(a),n=O(r),l=this.props.nextArgs?this.props.nextArgs:this.props.parentArgs;"object"===n?(e=De,"object"==typeof l.data&&null!==l.data||(l.data={})):"array"===n?(e=Ie,Array.isArray(l.data)||(l.data=[])):(e=Me,l.removable=!1,l.onMoveUp=null,l.onMoveDown=null,(Array.isArray(l.data)||"object"==typeof l.data)&&(l.data=null));var o=e(s({},l,{schema:r})),u=null;return this.props.nextArgs&&(u=this.props.nextArgs.schema.title||null),/*#__PURE__*/i.default.createElement("div",{className:"rjf-form-group rjf-oneof-group"},/*#__PURE__*/i.default.createElement("div",{className:"rjf-oneof-selector"},/*#__PURE__*/i.default.createElement(se,{value:a,options:this.getOptions(),onChange:function(e){return t.handleOptionChange(e,a)},className:"rjf-oneof-selector-input",label:u})),o)},t}(i.default.Component);function qe(e){/*#__PURE__*/return i.default.createElement(Fe,s({},e,{schemaName:"anyOf"}))}function He(e){if(!(e instanceof Object))return{isValid:!1,msg:"Schema must be an object"};var t,a=O(e);return(t="object"===a?Ue(e):"array"===a?Je(e):e.hasOwnProperty("allOf")?We(e):e.hasOwnProperty("oneOf")?ze(e):e.hasOwnProperty("anyOf")?Ke(e):e.hasOwnProperty("$ref")?{isValid:!0}:{isValid:!1,msg:"Outermost schema can only be of type array, list, object or dict"}).isValid&&e.hasOwnProperty("$defs")&&!e.$defs instanceof Object?{isValid:!1,msg:"'$defs' must be a valid JavaScript Object"}:t}function Ue(e){if(!(e.hasOwnProperty("keys")||e.hasOwnProperty("properties")||e.hasOwnProperty("oneOf")||e.hasOwnProperty("anyOf")||e.hasOwnProperty("allOf")))return{isValid:!1,msg:"Schema of type '"+e.type+"' must have at least one of these keys: ['properties' or 'keys' or 'oneOf' or 'anyOf' or 'allOf']"};var t,a=e.properties||e.keys;if(a&&(t=function(e){if(!(e instanceof Object))return{isValid:!1,msg:"The 'keys' or 'properties' key must be a valid JavaScript Object"};for(var t in e)if(e.hasOwnProperty(t)){var a=e[t];if(!(a instanceof Object))return{isValid:!1,msg:"Key '"+t+"' must be a valid JavaScript Object"};var r={isValid:!0},n=k(a.type);if(n?"object"===n?r=Ue(a):"array"===n&&(r=Je(a)):r=a.hasOwnProperty("$ref")?Be(a):a.hasOwnProperty("oneOf")?ze(a):a.hasOwnProperty("anyOf")?Ke(a):a.hasOwnProperty("allOf")?We(a):a.hasOwnProperty("const")?{isValid:!0,msg:""}:{isValid:!1,msg:"Key '"+t+"' must have a 'type' or a '$ref"},!r.isValid)return r}return{isValid:!0,msg:""}}(a),!t.isValid))return t;if(e.hasOwnProperty("additionalProperties")){if(!(e.additionalProperties instanceof Object)&&"boolean"!=typeof e.additionalProperties)return{isValid:!1,msg:"'additionalProperties' must be either a JavaScript boolean or a JavaScript object"};if(e.additionalProperties instanceof Object)if(e.additionalProperties.hasOwnProperty("$ref")){if(!(t=Be(e.additionalProperties)).isValid)return t}else{var r=k(e.additionalProperties.type);if("object"===r)return Ue(e.additionalProperties);if("array"===r)return He(e.additionalProperties)}}return e.hasOwnProperty("oneOf")&&!(t=ze(e)).isValid||e.hasOwnProperty("anyOf")&&!(t=Ke(e)).isValid||e.hasOwnProperty("allOf")&&!(t=We(e)).isValid?t:{isValid:!0,msg:""}}function Je(e){if(!e.hasOwnProperty("items"))return{isValid:!1,msg:"Schema of type '"+e.type+"' must have a key called 'items'"};if(!(e.items instanceof Object))return{isValid:!1,msg:"The 'items' key must be a valid JavaScript Object'"};var t=k(e.items.type);if(t){if("object"===t)return Ue(e.items);if("array"===t)return Je(e.items)}else{if(e.items.hasOwnProperty("$ref"))return Be(e.items);if(!(e.items.hasOwnProperty("oneOf")||e.items.hasOwnProperty("anyOf")||e.items.hasOwnProperty("allOf")||e.items.hasOwnProperty("const")))return{isValid:!1,msg:"Array 'items' must have a 'type' or '$ref' or 'oneOf' or 'anyOf'"}}return e.items.hasOwnProperty("oneOf")&&(validation=ze(e.items),!validation.isValid)||e.items.hasOwnProperty("anyOf")&&(validation=Ke(e.items),!validation.isValid)?validation:e.items.hasOwnProperty("allOf")?{isValid:!1,msg:"Currently, 'allOf' inside array items is not supported"}:e.items.hasOwnProperty("const")&&(validation={isValid:!0,msg:""},!validation.isValid)?validation:{isValid:!0,msg:""}}function Be(e){return"string"!=typeof e.$ref?{isValid:!1,msg:"'$ref' keyword must be a string"}:e.$ref.startsWith("#")?e.$ref.lenght>1&&!e.$ref.startsWith("#/")?{isValid:!1,msg:"Invalid '$ref' path"}:{isValid:!0,msg:""}:{isValid:!1,msg:"'$ref' value must begin with a hash (#) character"}}function ze(e){return Ye(e,"oneOf")}function Ke(e){return Ye(e,"anyOf")}function We(e){var t=Ye(e,"allOf");if(!t.isValid)return t;for(var a=e.allOf,r=0;r<a.length;r++)if("object"!==O(a[r]))return{isValid:!1,msg:"Possible conflict in 'allOf' subschemas. Currently, we only support subschemas listed in 'allOf' to be of type 'object'."};return t}function Ye(e,t){var a=e[t];if(!Array.isArray(a))return{isValid:!1,msg:"'"+t+"' property must be an array"};if(!a.length)return{isValid:!1,msg:"'"+t+"' must contain at least one subschema"};for(var r=0;r<a.length;r++){var n=a[r],i=O(n);if("object"===i){var l=Ue(n);if(!l.isValid)return l}else if("array"===i){var o=Je(n);if(!o.isValid)return o}}return{isValid:!0,msg:""}}var Ge=/*#__PURE__*/function(){function e(e){this.state=e}e.create=function(t,a){"string"==typeof t&&(t=JSON.parse(t));var r=He(t);if(!r.isValid)throw new Error("Error while creating EditorState: Invalid schema: "+r.msg);if("string"==typeof a&&""!==a&&(a=JSON.parse(a)),a)try{a=function(e,t,a){t.hasOwnProperty("$ref")&&delete(t=s({},a(t.$ref),t)).$ref;var r=T(O(t));return r?r(e,t,a):e}(a,t,function(a){return e.getRef(a,t)})}catch(e){throw console.error("Error while creating EditorState: Schema and data structure don't match"),e}else a=R(t,function(a){return e.getRef(a,t)});return new e({schema:t,data:a})},e.getRef=function(e,t){for(var a,r=e.split("/"),n=0;n<r.length;n++){var i=r[n];a="#"===i?t:a[i]}return s({},a)},e.update=function(t,a){return new e(s({},t._getState(),{data:a}))};var t=e.prototype;return t._getState=function(){return this.state},t.getData=function(){return this._getState().data},t.getSchema=function(){return this._getState().schema},e}(),Xe=/*#__PURE__*/function(e){function t(){for(var t,a=arguments.length,r=new Array(a),n=0;n<a;n++)r[n]=arguments[n];return(t=e.call.apply(e,[this].concat(r))||this).handleChange=function(e,a){(e=w(e)).shift();var r=Ze(e,JSON.parse(JSON.stringify(t.props.editorState.getData())),a);t.props.onChange(Ge.update(t.props.editorState,r))},t.getRef=function(e){return Ge.getRef(e,t.props.editorState.getSchema())},t.getFields=function(){var e=t.props.editorState.getData(),a=t.props.editorState.getSchema();a.hasOwnProperty("$ref")&&delete(a=s({},t.getRef(a.$ref),a)).$ref;var r=O(a),n={data:e,schema:a,name:"rjf",onChange:t.handleChange,onAdd:t.addFieldset,onRemove:t.removeFieldset,onEdit:t.editFieldset,onMove:t.moveFieldset,level:0,getRef:t.getRef,errorMap:t.props.errorMap||{}};return t.props.readonly&&(n.schema.readOnly=!0),"array"===r?Ie(n):"object"===r?De(n):"oneOf"===r?Ve(n):"anyOf"===r?Te(n):"allOf"===r?Le(n):[]},t.addFieldset=function(e,a){(a=w(a)).shift();var r=Qe(a,JSON.parse(JSON.stringify(t.props.editorState.getData())),e);t.props.onChange(Ge.update(t.props.editorState,r))},t.removeFieldset=function(e){(e=w(e)).shift();var a=et(e,JSON.parse(JSON.stringify(t.props.editorState.getData())));t.props.onChange(Ge.update(t.props.editorState,a))},t.editFieldset=function(e,a,r){(a=w(a)).shift(),(r=w(r)).shift();var n=Qe(a,JSON.parse(JSON.stringify(t.props.editorState.getData())),e);n=et(r,n),t.props.onChange(Ge.update(t.props.editorState,n))},t.moveFieldset=function(e,a){(e=w(e)).shift(),(a=w(a)).shift();var r=tt(e,a,JSON.parse(JSON.stringify(t.props.editorState.getData())));t.props.onChange(Ge.update(t.props.editorState,r))},t}return u(t,e),t.prototype.render=function(){/*#__PURE__*/return i.default.createElement("div",{className:"rjf-form-wrapper"},/*#__PURE__*/i.default.createElement("fieldset",{className:"module aligned"},/*#__PURE__*/i.default.createElement(h.Provider,{value:{fileHandler:this.props.fileHandler,fileHandlerArgs:this.props.fileHandlerArgs}},this.getFields())))},t}(i.default.Component);function Ze(e,t,a){var r=e.shift();return isNaN(Number(r))||(r=Number(r)),e.length?t[r]=Ze(e,t[r],a):void 0===r?t=a:t[r]=a,t}function Qe(e,t,a){var r=e.shift();return isNaN(Number(r))||(r=Number(r)),e.length?t[r]=Qe(e,t[r],a):Array.isArray(t[r])?t[r].push(a):Array.isArray(t)?t.push(a):t[r]=a,t}function et(e,t){var a=e.shift();return isNaN(Number(a))||(a=Number(a)),e.length?et(e,t[a]):Array.isArray(t)?t.splice(a,1):delete t[a],t}function tt(e,t,a){var r=e.shift();if(isNaN(Number(r))||(r=Number(r)),e.length)tt(e,t,a[r]);else if(Array.isArray(a)){var n=t[t.length-1],i=a[r];a.splice(r,1),a.splice(n,0,i)}return a}function at(e){this.schema=e,this.errorMap={},this.validate=function(t){this.errorMap={};var a=this.getValidator(O(e));a?a(this.schema,t,""):this.addError("",'Invalid schema type: "'+e.type+'"');var r={isValid:!0,errorMap:this.errorMap};return Object.keys(this.errorMap).length&&(r.isValid=!1),r},this.getValidator=function(e){var t;switch(e=k(e)){case"array":t=this.validateArray;break;case"object":t=this.validateObject;break;case"allOf":t=this.validateAllOf;break;case"oneOf":t=this.validateOneOf;break;case"anyOf":t=this.validateAnyOf;break;case"string":t=this.validateString;break;case"boolean":t=this.validateBoolean;break;case"integer":t=this.validateInteger;break;case"number":t=this.validateNumber}return t?t.bind(this):t},this.getRef=function(e){return Ge.getRef(e,this.schema)},this.addError=function(e,t){this.errorMap.hasOwnProperty(e)||(this.errorMap[e]=[]),this.errorMap[e].push(t)},this.joinCoords=function(e){var t=b.apply(null,e);return t.startsWith(f)&&(t=t.slice(1)),t},this.validateArray=function(e,t,a){if(Array.isArray(t)){var r=e.items;r.hasOwnProperty("$ref")&&(r=this.getRef(r.$ref));var n=k(r.type),i=S(e,"minItems","min_items"),l=S(e,"maxItems","max_items"),o=S(e.items,"choices","enum");if(i&&t.length<parseInt(i)&&this.addError(a,"Minimum "+i+" items required."),l&&t.length>parseInt(l)&&this.addError(a,"Maximum "+l+" items allowed."),N(e,"uniqueItems")&&("array"===n||"object"===n?t.length!==new Set(t.map(function(e){return JSON.stringify(e)})).size&&this.addError(a,"All items in this list must be unique."):t.length!==new Set(t).size&&this.addError(a,"All items in this list must be unique.")),o){var s=t.find(function(e){return-1===o.indexOf(e)});void 0!==s&&this.addError(a,'Invalid choice + "'+s+'"')}var u=this.getValidator(n);if(u||(r.hasOwnProperty("oneOf")?u=this.validateOneOf:r.hasOwnProperty("anyOf")?u=this.validateAnyOf:r.hasOwnProperty("anyOf")),u)for(var c=0;c<t.length;c++)u(r,t[c],this.joinCoords([a,c]));else this.addError(a,'Unsupported type "'+n+'" for array items.')}else this.addError(a,"Invalid data type. Expected array.")},this.validateObject=function(e,t,a){if("object"!=typeof t||Array.isArray(t))this.addError(a,"Invalid data type. Expected object.");else{var r=S(e,"properties","keys",{}),n=Object.keys(t),i=Object.keys(r).filter(function(e){return-1===n.indexOf(e)});if(i.length)this.addError(a,"These fields are missing from the data: "+i.join(", "));else{for(var l in t)if(t.hasOwnProperty(l)){var o=void 0;if(r.hasOwnProperty(l))o=r[l];else{if(!e.hasOwnProperty("additionalProperties"))continue;!0===(o=e.additionalProperties)&&(o={type:"string"})}o.hasOwnProperty("$ref")&&(o=this.getRef(o.$ref)),e.hasOwnProperty("required")&&Array.isArray(e.required)&&e.required.indexOf(l)>-1&&!o.hasOwnProperty("required")&&(o.required=!0);var s=k(o.type),u=this.getValidator(s);if(!u)return void this.addError(a,'Unsupported type "'+s+'" for object properties (keys).');u(o,t[l],this.joinCoords([a,l]))}e.hasOwnProperty("allOf")&&this.validateAllOf(e,t,a)}}},this.validateAllOf=function(e,t,a){for(var r={type:"object",properties:{}},n=0;n<e.allOf.length;n++){var i=e.allOf[n];i.hasOwnProperty("$ref")&&(i=this.getRef(i.$ref));var l=S(i,"properties","keys",{});for(var o in l)r.properties[o]=l[o]}this.validateObject(r,t,a)},this.validateOneOf=function(e,t,a){},this.validateAnyOf=function(e,t,a){},this.validateString=function(e,t,a){if(!e.required||t)if("string"==typeof t){if(t)if(e.minLength&&t.length<parseInt(e.minLength)&&this.addError(a,"This value must be at least "+e.minLength+" characters long."),(e.maxLength||0==e.maxLength)&&t.length>parseInt(e.maxLength)&&this.addError(a,"This value may not be longer than "+e.maxLength+" characters."),C(e,t)){var r;switch(k(e.format)){case"email":r=this.validateEmail;break;case"date":r=this.validateDate;break;case"time":r=this.validateTime;break;case"date-time":r=this.validateDateTime}r&&r.call(this,e,t,a)}else this.addError(a,'Invalid choice "'+t+'"')}else this.addError(a,"This value is invalid. Must be a valid string.");else this.addError(a,"This field is required.")},this.validateBoolean=function(e,t,a){e.required&&null==t?this.addError(a,"This field is required."):"boolean"!=typeof t&&null!=t&&this.addError(a,"Invalid value.")},this.validateInteger=function(e,t,a){e.required&&null==t?this.addError(a,"This field is required."):null!==t&&("number"==typeof t&&t===parseInt(t)?this.validateNumber(e,t,a):this.addError(a,"Invalid value. Only integers allowed."))},this.validateNumber=function(e,t,a){e.required&&null==t?this.addError(a,"This field is required."):null!==t&&("number"==typeof t?((e.minimum||0===e.minimum)&&t<e.minimum&&this.addError(a,"This value must not be less than "+e.minimum),(e.maximum||0===e.maximum)&&t>e.maximum&&this.addError(a,"This value must not be greater than "+e.maximum),(e.exclusiveMinimum||0===e.exclusiveMinimum)&&t<=e.exclusiveMinimum&&this.addError(a,"This value must be greater than "+e.exclusiveMinimum),(e.exclusiveMaximum||0===e.exclusiveMaximum)&&t>=e.exclusiveMaximum&&this.addError(a,"This value must be less than "+e.exclusiveMaximum),(e.multipleOf||0===e.multipleOf)&&100*t%(100*e.multipleOf)/100&&this.addError(a,"This value must be a multiple of "+e.multipleOf),C(e,t)||this.addError(a,'Invalid choice "'+t+'"')):this.addError(a,"Invalid value. Only numbers allowed."))},this.validateEmail=function(e,t,a){t.indexOf(" ")>-1?this.addError(a,"Enter a valid email address."):t.length>320&&this.addError(a,"Email may not be longer than 320 characters")},this.validateDate=function(e,t,a){},this.validateTime=function(e,t,a){},this.validateDateTime=function(e,t,a){}}function rt(e){this.containerId=e.containerId,this.dataInputId=e.dataInputId,this.schema=e.schema,this.data=e.data,this.errorMap=e.errorMap,this.fileHandler=e.fileHandler,this.fileHandlerArgs=e.fileHandlerArgs||{},this.readonly=e.readonly||!1,this.eventListeners=null,this._dataSynced=!1,this.addEventListener=function(e,t){null===this.eventListeners&&(this.eventListeners={}),this.eventListeners.hasOwnProperty(e)||(this.eventListeners[e]=new Set),this.eventListeners[e].add(t)},this.onChange=function(e){this.data=e.data,this._dataSynced?this.eventListeners&&this.eventListeners.hasOwnProperty("change")&&this.eventListeners.change.size&&this.eventListeners.change.forEach(function(t){return t(e)}):this._dataSynced=!0},this.onChange=this.onChange.bind(this),this.render=function(){o.default.hasOwnProperty("createRoot")?this._renderReact18():this._renderReact17()},this._renderReact17=function(){try{o.default.render(this._getFormContainerComponent(),document.getElementById(this.containerId))}catch(e){o.default.render(/*#__PURE__*/i.default.createElement(lt,{error:e}),document.getElementById(this.containerId))}},this._renderReact18=function(){var e=o.default.createRoot(document.getElementById(this.containerId));try{e.render(this._getFormContainerComponent())}catch(t){e.render(/*#__PURE__*/i.default.createElement(lt,{error:t}))}},this._getFormContainerComponent=function(){/*#__PURE__*/return i.default.createElement(it,{schema:this.schema,dataInputId:this.dataInputId,data:this.data,errorMap:this.errorMap,fileHandler:this.fileHandler,fileHandlerArgs:this.fileHandlerArgs,onChange:this.onChange,readonly:this.readonly})},this.update=function(e){this.schema=e.schema||this.schema,this.data=e.data||this.data,this.errorMap=e.errorMap||this.errorMap,this.render()},this.getSchema=function(){return this.schema},this.getData=function(){return this.data},this.validate=function(){return new at(this.getSchema()).validate(this.getData())}}var nt={},it=/*#__PURE__*/function(e){function t(t){var a;return(a=e.call(this,t)||this).populateDataInput=function(e){a.dataInput.value=JSON.stringify(e)},a.handleChange=function(e){a.setState({editorState:e})},a.state={editorState:Ge.create(t.schema,t.data)},a.prevEditorState=a.state.editorState,a.dataInput=document.getElementById(t.dataInputId),a}u(t,e);var a=t.prototype;return a.componentDidMount=function(){this.props.onChange({data:this.state.editorState.getData()}),this.populateDataInput(this.state.editorState.getData())},a.componentDidUpdate=function(e,t){if(this.props.schema===e.schema)this.props.data===e.data?(this.state.editorState!==t.editorState&&this.populateDataInput(this.state.editorState.getData()),this.props.onChange&&this.state.editorState!==t.editorState&&this.props.onChange({schema:this.state.editorState.getSchema(),data:this.state.editorState.getData(),prevSchema:t.editorState.getSchema(),prevData:t.editorState.getData()})):this.setState({editorState:Ge.update(this.state.editorState,this.props.data)});else{var a=this.props.schema,r=this.props.data!==e.data?this.props.data:this.state.editorState.getData();this.setState({editorState:Ge.create(a,r)})}},a.render=function(){/*#__PURE__*/return i.default.createElement(Xe,{editorState:this.state.editorState,onChange:this.handleChange,fileHandler:this.props.fileHandler,fileHandlerArgs:this.props.fileHandlerArgs,errorMap:this.props.errorMap,readonly:this.props.readonly})},t}(i.default.Component);function lt(e){/*#__PURE__*/return i.default.createElement("div",{style:{color:"#f00"}},/*#__PURE__*/i.default.createElement("p",null,"(!) ",e.error.toString()),/*#__PURE__*/i.default.createElement("p",null,"Check browser console for more details about the error."))}e.DataValidator=at,e.EditorState=Ge,e.ReactJSONForm=Xe,e.createForm=function(e){var t=new rt(e);return nt[e.containerId]=t,t},e.getFormInstance=function(e){return nt[e]}});
