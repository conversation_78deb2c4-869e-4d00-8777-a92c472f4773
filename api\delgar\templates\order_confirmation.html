<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Order {{ order_id }} - Delgar Frozen Products</title>
  <style>
    body {
      font-family: 'Segoe UI', sans-serif;
      background-color: #ffffff;
      margin: 0;
      padding: 0;
      color: #333;
    }
  </style>
</head>
<body style="margin:0; padding:0; background-color:#ffffff;">
  <div style="max-width:700px; margin:40px auto; background-color:#ffffff; padding:30px; border-radius:10px; box-shadow:0 0 8px rgba(0,0,0,0.05); font-family:'Segoe UI', sans-serif; color:#333;">
    <h2 style="color:#18863d; text-align:center; margin-bottom:30px;">
      Thank You for Your Order!
    </h2>

    <p>
      Hello
      <strong style="text-transform: capitalize;">{{ first_name }}</strong>,
    </p>
    <p>
      We’ve received your order placed on
      <strong>{{ created_at }}</strong>. Below is a summary of your order:
    </p>

    <div style="margin-top:20px;">
      <h3 style="border-bottom:1px solid #ddd; padding-bottom:8px; margin-bottom:12px; font-size:18px;">
        🛍️ Order Summary
      </h3>
      <table style="width:100%; border-collapse:collapse;">
        <thead>
          <tr>
            <th style="padding:12px; background-color:#f1f1f1; text-align:left; font-size:14px;">
              Product
            </th>
            <th style="padding:12px; background-color:#f1f1f1; text-align:left; font-size:14px;">
              Qty
            </th>
            <th style="padding:12px; background-color:#f1f1f1; text-align:left; font-size:14px;">
              Sub Total
            </th>
            <th style="padding:12px; background-color:#f1f1f1; text-align:left; font-size:14px;">
              Type
            </th>
          </tr>
        </thead>
        <tbody>
          {% for product in products %}
          <tr>
            <td style="padding:12px; border-bottom:1px solid #eee; font-size:14px; text-transform:uppercase;">
              {{ product.product_name|upper }}
            </td>
            <td style="padding:12px; border-bottom:1px solid #eee; font-size:14px; text-transform:uppercase;">
              {{ product.quantity }}
            </td>
            <td style="padding:12px; border-bottom:1px solid #eee; font-size:14px; text-transform:uppercase;">
              {{ product.total_amount }}
            </td>
            <td style="padding:12px; border-bottom:1px solid #eee; font-size:14px; text-transform:uppercase;">
              {{ product.purchase_type|upper }}
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>

    <div style="margin-top:20px; font-size:14px;">
      <p style="margin:4px 0;">
        <strong style="color:#e60000; font-size:16px;">
          Grand Total: ₱{{ grand_total }}
        </strong>
      </p>
    </div>

    <div style="margin-top:20px;">
      <h3 style="border-bottom:1px solid #ddd; padding-bottom:8px; margin-bottom:12px; font-size:18px;">
        📇 Contact & Payment Info
      </h3>
      <p style="text-transform: capitalize;">
        <strong>Full Name:</strong>
        {{ fullname }}
      </p>
      {% if delivery_address %}
      <p style="text-transform: capitalize;">
        <strong>Delivery Address:</strong>
        {{ delivery_address }}
      </p>
      {% endif %}
      <p>
        <strong>Email:</strong>
        {{ contact_email }}
      </p>
      <p>
        <strong>Phone:</strong>
        {{ contact_number }}
      </p>
      <p>
        <strong>Payment Method:</strong>
        {{ payment_method }}
      </p>
      {% if receipt_url and receipt_url != 'https://www.delgar.store' %}
      <div style="margin-top: 20px;">
        <p>
          <strong>Receipt Proof:</strong>
        </p>
        <p>
          <img alt="Uploaded Receipt"
               src="{{ receipt_url }}"
               style="width:100%; border-radius:6px; border:1px solid #ccc;" />
        </p>
        <p></p>
      </div>
      {% endif %}
    </div>

    <div style="text-align:center; font-size:13px; color:#777; margin-top:40px;">
      <div>
        If you have any questions about your order, just reply to this email or contact us on Facebook.
      </div>
      <div>
        Thank you for shopping with
        <strong>Delgar Frozen Products</strong>!
      </div>

      <!-- Footer Info Start -->
      <div style="margin-top:30px; font-size:13px; color:#555;">
        <div>
          🕒
          <strong>Easy Online Order 24/7</strong>
        </div>
        <div>📞 +63 997 866 5777</div>
        <div style="margin-bottom: 20px;">
          🌐
          <a href="https://www.delgar.store"
             style="color:#18863d; text-decoration:none;">
            www.delgar.store
          </a>
        </div>
        <div>
          <strong>Store Address:</strong>
          JP Rizal Avenue, In front of Penshoppe beside Sanbon Enterprises,<br>
          Purok 4 Carmen Annex, Ozamiz City, Philippines <br>
          <strong>Store Hours:</strong> 8:00 AM – 6:00 PM
        </div>
       
      </div>
      <!-- Footer Info End -->
    </div>
  </div>
</body>
</html>
