// generated by the @nuxtjs/tailwindcss <https://github.com/nuxt-modules/tailwindcss> module at 9/14/2025, 8:14:52 PM
import "@nuxtjs/tailwindcss/config-ctx"
import configMerger from "@nuxtjs/tailwindcss/merger";

import cfg2 from "./../../tailwind.config.js";
const config = [
{"content":{"files":["C:/Users/<USER>/OneDrive/Desktop/www.delgar.store/www.delgar.store/frontend/components/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/OneDrive/Desktop/www.delgar.store/www.delgar.store/frontend/components/global/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/OneDrive/Desktop/www.delgar.store/www.delgar.store/frontend/components/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/OneDrive/Desktop/www.delgar.store/www.delgar.store/frontend/layouts/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/OneDrive/Desktop/www.delgar.store/www.delgar.store/frontend/plugins/**/*.{js,ts,mjs}","C:/Users/<USER>/OneDrive/Desktop/www.delgar.store/www.delgar.store/frontend/composables/**/*.{js,ts,mjs}","C:/Users/<USER>/OneDrive/Desktop/www.delgar.store/www.delgar.store/frontend/utils/**/*.{js,ts,mjs}","C:/Users/<USER>/OneDrive/Desktop/www.delgar.store/www.delgar.store/frontend/pages/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/OneDrive/Desktop/www.delgar.store/www.delgar.store/frontend/{A,a}pp.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/OneDrive/Desktop/www.delgar.store/www.delgar.store/frontend/{E,e}rror.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/OneDrive/Desktop/www.delgar.store/www.delgar.store/frontend/app.config.{js,ts,mjs}"]}},
{},
cfg2
].reduce((acc, curr) => configMerger(acc, curr), {});

const resolvedConfig = config;

export default resolvedConfig;