{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxtjs/tailwindcss": "^6.14.0", "axios": "^1.12.1", "chart.js": "^4.5.0", "moment": "^2.30.1", "nuxt": "^3.17.6", "nuxt-gtag": "^3.0.3", "vue": "^3.5.17", "vue-chartjs": "^5.3.2", "vue-router": "^4.5.1"}, "devDependencies": {"nuxt-facebook-chat": "^1.0.5"}}