from django.conf import settings
from django.contrib import admin
from django.urls import include, path
from django.urls import re_path
from django.conf.urls.static import static
# from django.views.static import serve

urlpatterns = [
    re_path('', include('landingPage.urls')),
    path('admin/', admin.site.urls),
    # path('api/', include('djoser.urls')),
    # path('api/', include('djoser.urls.authtoken')),
    path('api/email-notification/', include('email_notification.urls')),
    path('api/register/', include('register.urls')),
    path('api/delgar/', include('delgar.urls')),
    path('api/fabricsplus/', include('fabricsplus.urls'))
]

urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)