# Generated by Django 5.0.2 on 2025-06-05 10:55

import django_jsonform.models.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('register', '0003_donationmodel'),
    ]

    operations = [
        migrations.CreateModel(
            name='RegistrationModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('registration_id', models.Char<PERSON>ield(blank=True, default='', max_length=255, null=True)),
                ('fullname', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('contact_email', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('contact_number', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('payment_method', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('base_amount', models.Char<PERSON>ield(blank=True, default='', max_length=255, null=True)),
                ('member', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('add_ons', django_jsonform.models.fields.JSONField(null=True)),
                ('receipt_url', models.CharField(blank=True, default='', max_length=500, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ('-created_at',),
            },
        ),
    ]
