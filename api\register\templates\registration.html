<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Registration Confirmation</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <style>
    body, table, td, p {
      margin: 0;
      padding: 0;
      line-height: 1.5;
    }

    body {
      background-color: #f4f4f4;
      font-family: Arial, sans-serif;
      color: #333;
      padding: 20px;
    }

    .email-container {
      max-width: 600px;
      width: 100%;
      margin: 0 auto;
      background: white;
      border-radius: 10px;
      overflow: hidden;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
      border: 1px solid #ddd;
    }

    .email-header {
      background-color: #6A0DAD;
      color: white;
      text-align: center;
      padding: 7px 2px 5px 2px;
      font-size: 22px;
      font-weight: bold;
      letter-spacing: 1px;
    }

    .email-content {
      padding: 25px 20px;
    }

    .email-body {
      width: 100%;
      border-collapse: collapse;
      font-size: 15px;
    }

    .email-body td {
      padding: 8px 4px;
      vertical-align: top;
    }

    .email-body td:first-child {
      font-weight: bold;
      font-size: 12px;
      width: 130px;
      color: #6A0DAD;
    }

    .email-body ul {
      margin: 0;
      padding-left: 20px;
    }

    .btn {
      display: inline-block;
      padding: 8px 16px;
      background-color: #6A0DAD;
      color: white;
      text-decoration: none;
      border-radius: 5px;
      font-size: 14px;
      margin-top: 8px;
    }

    .qr-code {
      text-align: center;
      margin-top: 25px;
    }

    .qr-code img {
      max-width: 100%;
      height: auto;
      margin-top: 10px;
    }

    .footer {
      background-color: #eee;
      text-align: center;
      padding: 15px;
      font-size: 12px;
      color: #777;
    }

@media only screen and (max-width: 600px) {
  body {
    padding: 10px;
  }

  .email-header {
    font-size: 20px;
    padding: 10px 2px 5px 2px;
  }

  .email-content {
    padding: 20px 15px;
  }

  .email-body {
    display: block;
  }

  .email-body tr {
    display: flex;
    margin-bottom: 12px;
    border-bottom: 1px solid #ddd;
  }

  .email-body td {
    display: block;
    width: 100%;
    padding: 4px 0;
  }

  .email-body td:first-child {
    font-weight: bold;
    font-size: 14px;
    color: #6A0DAD;
  }

  .btn {
    font-size: 13px;
    padding: 7px 10px;
  }
}

  </style>
</head>
<body>

  <table class="email-container" cellpadding="0" cellspacing="0">
    <!-- Header -->
    <tr>
      <td class="email-header">
       <span style="display: inline-flex; align-items: center; gap: 10px;">
          TEAM BUILDING TICKET
        </span>
      </td>
    </tr>

    <!-- Body -->
    <tr>
      <td class="email-content">
        <p style="font-size: 16px; margin-bottom: 15px;">Thank you for registering for our <strong>Team Building Event</strong>!</p>
        <p style="font-size: 15px; margin-bottom: 20px;">Here are your registration details:</p>

        <table class="email-body">
          <tr>
            <td>Registration ID:</td>
            <td>{{ registration_id }}</td>
          </tr>
          <tr>
            <td>Full Name:</td>
            <td>{{ fullname|upper }}</td>
          </tr>
          <tr>
            <td>Email:</td>
            <td>{{ contact_email }}</td>
          </tr>
          <tr>
            <td>Contact Number:</td>
            <td>{{ contact_number }}</td>
          </tr>
          {% if payment_method and payment_method|stringformat:"s"|lower != 'none' and payment_method|stringformat:"s" != '' %}
          <tr>
            <td>Payment Method:</td>
            <td>{{ payment_method }}</td>
          </tr>
          {% endif %}
          {% if base_amount and base_amount|stringformat:"s"|lower != 'none' and base_amount|stringformat:"s" != '' %}
          <tr>
            <td>Total Amount:</td>
            <td>PHP {{ base_amount }}</td>
          </tr>
          {% endif %}
          <tr>
            <td>Member:</td>
            <td>{{ member }}</td>
          </tr>
          <tr>
            <td>Add-ons:</td>
            <td>
              {% if add_ons %}
                <ul>
                  {% for item in add_ons %}
                    <li>{{ item }}</li>
                  {% endfor %}
                </ul>
              {% else %}
                <em>None</em>
              {% endif %}
            </td>
          </tr>
          {% if receipt_url and receipt_url != 'https://techsavvies.space/' %}
          <tr>
            <td>Receipt:</td>
            <td>
              <a href="{{ receipt_url }}" target="_blank" class="btn" style="color:#f4f4f4">Download Receipt</a>
            </td>
          </tr>
          {% endif %}
        </table>

        <!-- QR Code -->
        <div class="qr-code">
          <p style="font-size: 14px;">Scan the QR code to visit the event page:</p>
          <img src="https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=https://techsavvies.space/tbs3" alt="Event QR Code" />
        </div>

        <p style="margin-top: 25px; font-size: 14px;">If you have any questions, feel free to reply to this email.</p>
        <p style="font-size: 14px;">Regards,<br><strong>TECHSAVVY Admin</strong></p>
      </td>
    </tr>

    <!-- Footer -->
    <tr>
      <td class="footer">
        &copy; 2025 TECHSAVVY. All rights reserved.
      </td>
    </tr>
  </table>

</body>
</html>
