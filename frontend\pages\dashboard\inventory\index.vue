<script setup>
import { ref, onMounted } from "vue";
import moment from "moment";

// Reactive state
const isClient = ref(false);
let axios = null;

// Initialize client-side only functionality
onMounted(async () => {
  isClient.value = true;
  try {
    const axiosModule = await import('axios');
    axios = axiosModule.default;
    console.log('✅ Axios loaded successfully');
  } catch (err) {
    console.error('❌ Failed to load axios:', err);
  }
});

const product = ref({
  product_id: "PID" + moment().valueOf(),
  product_image: [], // will hold array of objects with name and url
  product_name: "",
  wholesale_price: "",
  retail_price: "",
  category: "",
  stocks_remaining: "",
});

const selectedFiles = ref([]);   // files chosen but not yet uploaded
const uploadedFiles = ref([]);   // successfully uploaded files
const uploadStatus = ref("");
const uploadProgress = ref(0);

const loading = ref(false);
const success = ref(null);
const error = ref(null);
const showToast = ref(false);

// Handle multiple file selection
const handleFileUpload = (event) => {
  if (!isClient.value) return; // ensure runs only in client

  const files = event.target.files;
  if (!files || files.length === 0) {
    selectedFiles.value = [];
    return;
  }

  selectedFiles.value = Array.from(files);
  uploadStatus.value = `${files.length} file(s) selected`;

  selectedFiles.value.forEach((file) => {
    console.log(`📁 Selected: ${file.name} (${(file.size / 1024).toFixed(1)} KB)`);
  });
};

// Upload a single file with better error handling
const uploadSingleFile = async (file) => {
  if (!isClient.value || !axios) {
    throw new Error('Upload service not available');
  }

  // Validate file
  if (!file || !file.name) {
    throw new Error('Invalid file');
  }

  // Create FormData safely
  let formData;
  try {
    formData = new FormData();
    formData.append("file", file);
  } catch (err) {
    throw new Error(`FormData creation failed: ${err.message}`);
  }

  const response = await axios.post(
    "http://127.0.0.1:8000/api/delgar/upload/",
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
      timeout: 30000, // 30 second timeout
    }
  );

  if (!response.data || !response.data.file) {
    throw new Error('Invalid response from upload service');
  }

  return {
    name: file.name,
    url: response.data.file,
  };
};

// Upload multiple files with progress tracking
const uploadFiles = async () => {
  if (!isClient.value || !axios) {
    uploadStatus.value = "Upload service not available";
    return [];
  }

  if (!selectedFiles.value.length) {
    uploadStatus.value = "No files selected";
    return [];
  }

  try {
    uploadStatus.value = "Starting upload...";
    uploadProgress.value = 0;

    // Upload files one by one with progress
    const uploadedResults = [];
    const totalFiles = selectedFiles.value.length;

    for (let i = 0; i < selectedFiles.value.length; i++) {
      const file = selectedFiles.value[i];

      try {
        uploadStatus.value = `Uploading ${file.name}... (${i + 1}/${totalFiles})`;

        const result = await uploadSingleFile(file);
        uploadedResults.push(result);

        uploadProgress.value = Math.round(((i + 1) / totalFiles) * 100);
        console.log(`✅ Uploaded: ${result.name} -> ${result.url}`);

      } catch (err) {
        console.error(`❌ Failed to upload ${file.name}:`, err);
        throw new Error(`Failed to upload ${file.name}: ${err.message}`);
      }
    }

    uploadStatus.value = `✅ All ${totalFiles} images uploaded successfully!`;
    console.log("📸 All images uploaded:", uploadedResults);

    uploadedFiles.value = uploadedResults;
    product.value.product_image = uploadedResults; // save full objects with name and url
    selectedFiles.value = []; // clear after upload

    return uploadedResults;
  } catch (err) {
    console.error("❌ Upload error:", err);
    uploadStatus.value = "❌ Upload failed: " + err.message;
    uploadProgress.value = 0;
    return [];
  }
};

// Save product with comprehensive validation
const saveProduct = async () => {
  if (!isClient.value || !axios) {
    error.value = "❌ Save service not available";
    showToast.value = true;
    return;
  }

  loading.value = true;
  success.value = null;
  error.value = null;

  try {
    // Upload images first if any are pending
    if (selectedFiles.value.length) {
      console.log("📤 Uploading pending images...");
      await uploadFiles();
    }

    // Validate required fields
    if (!product.value.product_name.trim()) {
      throw new Error("Product name is required");
    }
    if (!product.value.wholesale_price || product.value.wholesale_price <= 0) {
      throw new Error("Valid wholesale price is required");
    }
    if (!product.value.retail_price || product.value.retail_price <= 0) {
      throw new Error("Valid retail price is required");
    }

    console.log("📦 Sending product data:", product.value);
    console.log("🖼️ Product images:", product.value.product_image);

    const response = await axios.post(
      "http://127.0.0.1:8000/api/delgar/product/create/",
      product.value,
      {
        timeout: 15000, // 15 second timeout
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    success.value = "✅ Product saved successfully!";
    error.value = null;
    showToast.value = true;

    console.log("✅ Product saved:", response.data);

    // Reset form
    product.value = {
      product_id: "PID" + moment().valueOf(),
      product_image: [],
      product_name: "",
      wholesale_price: "",
      retail_price: "",
      category: "",
      stocks_remaining: "",
    };
    uploadedFiles.value = [];
    uploadStatus.value = "";

  } catch (err) {
    console.error("❌ Save error:", err);

    // More specific error messages
    if (err.message.includes('required')) {
      error.value = `❌ ${err.message}`;
    } else if (err.response?.status === 400) {
      error.value = "❌ Invalid product data. Please check your inputs.";
    } else if (err.response?.status === 500) {
      error.value = "❌ Server error. Please try again later.";
    } else if (err.code === 'ECONNABORTED') {
      error.value = "❌ Request timeout. Please try again.";
    } else {
      error.value = "❌ Failed to save product. Please try again.";
    }

    success.value = null;
    showToast.value = true;
  } finally {
    loading.value = false;
    setTimeout(() => (showToast.value = false), 5000);
  }
};
</script>

<template>
  <div class="min-h-screen bg-gray-100 flex items-center justify-center p-6">
      <div class="w-full max-w-2xl bg-white rounded-2xl shadow-lg p-8 relative">
      <h1 class="text-2xl font-bold text-gray-800 mb-6">Inventory Dashboard</h1>

      <form class="space-y-5" @submit.prevent="saveProduct">
        <!-- Product Images -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">
            Product Images
          </label>
          <input
            type="file"
            multiple
            @change="handleFileUpload"
            class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none"
          />

          <div v-if="selectedFiles.length" class="mt-2 text-sm text-gray-600">
            Selected:
            <ul class="list-disc ml-5">
              <li v-for="(file, index) in selectedFiles" :key="index">
                {{ file.name }}
              </li>
            </ul>
          </div>

          <!-- Upload Status and Progress -->
          <div v-if="uploadStatus" class="mt-3 p-3 bg-blue-50 rounded-lg">
            <div class="text-sm text-blue-800 mb-2">{{ uploadStatus }}</div>
            <div v-if="uploadProgress > 0 && uploadProgress < 100" class="w-full bg-blue-200 rounded-full h-2">
              <div
                class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                :style="`width: ${uploadProgress}%`"
              ></div>
            </div>
          </div>

          <div v-if="uploadedFiles.length" class="mt-3 p-3 bg-green-50 rounded-lg">
            <div class="text-sm font-medium text-green-800 mb-2">✅ Uploaded Images:</div>
            <div class="grid grid-cols-1 gap-2">
              <div v-for="(file, index) in uploadedFiles" :key="index"
                   class="flex items-center justify-between bg-white p-2 rounded border">
                <span class="text-sm font-medium text-gray-700">{{ file.name }}</span>
                <a :href="file.url" target="_blank"
                   class="text-blue-600 hover:text-blue-800 text-xs font-medium hover:underline">
                  View →
                </a>
              </div>
            </div>
          </div>

          <div v-if="uploadStatus" class="mt-2 text-xs text-gray-500">
            {{ uploadStatus }}
          </div>
        </div>

        <!-- Product Name -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">
            Product Name
          </label>
          <input
            v-model="product.product_name"
            type="text"
            placeholder="Enter product name"
            class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none"
          />
        </div>

        <!-- Wholesale & Retail Price -->
        <div class="flex gap-4">
          <div class="w-full">
            <label class="block text-sm font-medium text-gray-700 mb-1">
              Wholesale Price
            </label>
            <input
              v-model="product.wholesale_price"
              type="number"
              placeholder="0.00"
              class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none"
            />
          </div>
          <div class="w-full">
            <label class="block text-sm font-medium text-gray-700 mb-1">
              Retail Price
            </label>
            <input
              v-model="product.retail_price"
              type="number"
              placeholder="0.00"
              class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none"
            />
          </div>
        </div>

        <!-- Category -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">
            Category
          </label>
          <select
            v-model="product.category"
            class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none"
          >
            <option value="">Select category</option>
            <option>Chicken</option>
            <option>Pork</option>
            <option>Seafood</option>
            <option>Beef</option>
            <option>Others</option>
          </select>
        </div>

        <!-- Stocks Remaining -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">
            Stocks Remaining
          </label>
          <input
            v-model="product.stocks_remaining"
            type="number"
            placeholder="Enter stock count"
            class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none"
          />
        </div>

        <!-- Submit Button -->
        <div class="pt-4">
          <button
            type="submit"
            :disabled="loading"
            class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition disabled:opacity-50"
          >
            {{ loading ? "Saving..." : "Save Product" }}
          </button>
        </div>
      </form>

      <!-- Toast Notification -->
      <transition name="fade">
        <div
          v-if="showToast"
          class="fixed bottom-5 right-5 px-6 py-3 rounded-lg shadow-lg text-white"
          :class="success ? 'bg-green-600' : 'bg-red-600'"
        >
          {{ success || error }}
        </div>
      </transition>
    </div>
  </div>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
