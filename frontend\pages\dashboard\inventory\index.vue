<script setup>
import { ref } from "vue";
import moment from "moment";
import axios from "axios";

const product = ref({
  product_id: "PID" + moment().valueOf(),
  product_image: [], // will hold array of objects with name and url
  product_name: "",
  wholesale_price: "",
  retail_price: "",
  category: "",
  stocks_remaining: "",
});

const selectedFiles = ref([]);   // files chosen but not yet uploaded
const uploadedFiles = ref([]);   // successfully uploaded files
const uploadStatus = ref("");

const loading = ref(false);
const success = ref(null);
const error = ref(null);
const showToast = ref(false);

// Handle multiple file selection
const handleFileUpload = (event) => {
  if (process.server) return; // ensure runs only in client
  selectedFiles.value = Array.from(event.target.files);
  selectedFiles.value.forEach((file) => {
    console.log(`Selected file: ${file.name}, Size: ${file.size} bytes`);
  });
};

// Upload multiple files
const uploadFiles = async () => {
  if (process.server) return []; // prevent SSR execution
  if (!selectedFiles.value.length) return [];

  try {
    uploadStatus.value = "Uploading images...";

    // Upload files one by one since the API expects single file uploads
    const uploadPromises = selectedFiles.value.map(async (file) => {
      const formData = new window.FormData();
      formData.append("file", file);

      const response = await axios.post(
        "http://127.0.0.1:8000/api/delgar/upload/",
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      return {
        name: file.name,
        url: response.data.file,
      };
    });

    const uploaded = await Promise.all(uploadPromises);

    uploadStatus.value = "Upload successful!";
    console.log("Images uploaded:", uploaded);

    uploadedFiles.value = uploaded;
    product.value.product_image = uploaded; // save full objects with name and url
    selectedFiles.value = []; // clear after upload

    return uploaded;
  } catch (err) {
    console.error("Upload error:", err);
    uploadStatus.value = "Upload failed: " + err.message;
    return [];
  }
};

// Save product
const saveProduct = async () => {
  loading.value = true;
  success.value = null;
  error.value = null;

  try {
    // Upload images first if any are pending
    if (selectedFiles.value.length) {
      await uploadFiles();
    }

    console.log("Sending product data:", product.value);
    console.log("Product images data:", product.value.product_image);

    await axios.post("http://127.0.0.1:8000/api/delgar/product/create/", product.value);

    success.value = "✅ Product saved successfully!";
    error.value = null;
    showToast.value = true;

    console.log("Saved product:", product.value);

    // Reset form
    product.value = {
      product_id: "PID" + moment().valueOf(),
      product_image: [],
      product_name: "",
      wholesale_price: "",
      retail_price: "",
      category: "",
      stocks_remaining: "",
    };
    uploadedFiles.value = [];
  } catch (err) {
    error.value = "❌ Failed to save product.";
    success.value = null;
    console.error(err);
    showToast.value = true;
  } finally {
    loading.value = false;
    setTimeout(() => (showToast.value = false), 3000);
  }
};
</script>

<template>
  <div class="min-h-screen bg-gray-100 flex items-center justify-center p-6">
    <div class="w-full max-w-2xl bg-white rounded-2xl shadow-lg p-8 relative">
      <h1 class="text-2xl font-bold text-gray-800 mb-6">Inventory Dashboard</h1>

      <form class="space-y-5" @submit.prevent="saveProduct">
        <!-- Product Images -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">
            Product Images
          </label>
          <input
            type="file"
            multiple
            @change="handleFileUpload"
            class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none"
          />

          <div v-if="selectedFiles.length" class="mt-2 text-sm text-gray-600">
            Selected:
            <ul class="list-disc ml-5">
              <li v-for="(file, index) in selectedFiles" :key="index">
                {{ file.name }}
              </li>
            </ul>
          </div>

          <div v-if="uploadedFiles.length" class="mt-2 text-xs text-green-600">
            Uploaded:
            <ul class="list-disc ml-5">
              <li v-for="(file, index) in uploadedFiles" :key="index" class="mb-1">
                <span class="font-medium">{{ file.name }}</span>
                <br>
                <a :href="file.url" target="_blank" class="text-blue-500 hover:underline text-xs">
                  View Image
                </a>
              </li>
            </ul>
          </div>

          <div v-if="uploadStatus" class="mt-2 text-xs text-gray-500">
            {{ uploadStatus }}
          </div>
        </div>

        <!-- Product Name -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">
            Product Name
          </label>
          <input
            v-model="product.product_name"
            type="text"
            placeholder="Enter product name"
            class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none"
          />
        </div>

        <!-- Wholesale & Retail Price -->
        <div class="flex gap-4">
          <div class="w-full">
            <label class="block text-sm font-medium text-gray-700 mb-1">
              Wholesale Price
            </label>
            <input
              v-model="product.wholesale_price"
              type="number"
              placeholder="0.00"
              class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none"
            />
          </div>
          <div class="w-full">
            <label class="block text-sm font-medium text-gray-700 mb-1">
              Retail Price
            </label>
            <input
              v-model="product.retail_price"
              type="number"
              placeholder="0.00"
              class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none"
            />
          </div>
        </div>

        <!-- Category -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">
            Category
          </label>
          <select
            v-model="product.category"
            class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none"
          >
            <option value="">Select category</option>
            <option>Chicken</option>
            <option>Pork</option>
            <option>Seafood</option>
            <option>Beef</option>
            <option>Others</option>
          </select>
        </div>

        <!-- Stocks Remaining -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">
            Stocks Remaining
          </label>
          <input
            v-model="product.stocks_remaining"
            type="number"
            placeholder="Enter stock count"
            class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none"
          />
        </div>

        <!-- Submit Button -->
        <div class="pt-4">
          <button
            type="submit"
            :disabled="loading"
            class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition disabled:opacity-50"
          >
            {{ loading ? "Saving..." : "Save Product" }}
          </button>
        </div>
      </form>

      <!-- Toast Notification -->
      <transition name="fade">
        <div
          v-if="showToast"
          class="fixed bottom-5 right-5 px-6 py-3 rounded-lg shadow-lg text-white"
          :class="success ? 'bg-green-600' : 'bg-red-600'"
        >
          {{ success || error }}
        </div>
      </transition>
    </div>
  </div>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
