# Generated by Django 5.0.2 on 2025-05-16 04:31

import django_jsonform.models.fields
import storages.backends.s3
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('register', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='FileUploadModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(storage=storages.backends.s3.S3Storage(), upload_to='codecamp/receipt/uploads/')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.AddField(
            model_name='confirmreceiptmodel',
            name='payment_method',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='confirmreceiptmodel',
            name='preferred_topics',
            field=django_jsonform.models.fields.J<PERSON><PERSON>ield(null=True),
        ),
        migrations.AddField(
            model_name='confirmreceiptmodel',
            name='receipt_url',
            field=models.CharField(blank=True, default='', max_length=500, null=True),
        ),
        migrations.AddField(
            model_name='confirmreceiptmodel',
            name='registration_fee',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
    ]
