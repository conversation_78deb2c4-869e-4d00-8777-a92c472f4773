# Generated by Django 5.0.2 on 2025-05-05 16:44

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ConfirmReceiptModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('team_member_id', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('fullname', models.Char<PERSON>ield(blank=True, default='', max_length=255, null=True)),
                ('birth_date', models.Char<PERSON>ield(blank=True, default='', max_length=255, null=True)),
                ('contact_email', models.Char<PERSON>ield(blank=True, default='', max_length=255, null=True)),
                ('contact_number', models.Char<PERSON>ield(blank=True, default='', max_length=255, null=True)),
                ('tech_level', models.Char<PERSON><PERSON>(blank=True, default='', max_length=255, null=True)),
                ('data_privacy_consent', models.<PERSON>r<PERSON><PERSON>(blank=True, default='', max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ('-created_at',),
            },
        ),
    ]
