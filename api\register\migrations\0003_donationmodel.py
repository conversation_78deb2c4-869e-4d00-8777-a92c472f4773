# Generated by Django 5.0.2 on 2025-05-27 09:37

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('register', '0002_fileuploadmodel_confirmreceiptmodel_payment_method_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='DonationModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('donation_id', models.Char<PERSON>ield(blank=True, default='', max_length=255, null=True)),
                ('fullname', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('contact_email', models.Char<PERSON>ield(blank=True, default='', max_length=255, null=True)),
                ('amount', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('payment_method', models.Char<PERSON>ield(blank=True, default='', max_length=255, null=True)),
                ('receipt_url', models.Char<PERSON>ield(blank=True, default='', max_length=500, null=True)),
                ('created_at', models.DateTime<PERSON>ield(auto_now_add=True)),
            ],
            options={
                'ordering': ('-created_at',),
            },
        ),
    ]
