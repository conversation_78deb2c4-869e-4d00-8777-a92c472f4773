# Generated by Django 5.2 on 2025-04-26 13:56

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='EmailNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('incharge_firstname', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ('incharge_contact_email', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ('schedule', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ('approval_status', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ('remarks', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ('tracking_id', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ('purpose', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
            ],
        ),
    ]
